from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, UUID4

class QuoteBase(BaseModel):
    """报价基础模型"""
    supplier_name: str
    data: Dict[str, Any]
    attachments: Optional[Dict[str, Any]] = None
    is_registered: Optional[bool] = False
    quote_time: Optional[datetime] = None

class QuoteCreate(QuoteBase):
    """创建报价模型"""
    supplier_id: Optional[UUID4] = None

class QuoteUpdate(BaseModel):
    """更新报价模型"""
    supplier_name: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    attachments: Optional[Dict[str, Any]] = None
    is_registered: Optional[bool] = None
    supplier_id: Optional[UUID4] = None
    quote_time: Optional[datetime] = None

class Quote(QuoteBase):
    """报价响应模型"""
    id: UUID4
    task_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by_id: Optional[UUID4] = None

    class Config:
        from_attributes = True

class QuoteSummaryItem(BaseModel):
    """报价汇总项模型"""
    field_name: str
    field_label: str
    field_type: str
    values: List[Dict[str, Any]]
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    avg_value: Optional[float] = None

class QuoteSummary(BaseModel):
    """报价汇总模型"""
    task_id: UUID4
    task_title: str
    supplier_count: int
    items: List[QuoteSummaryItem]
