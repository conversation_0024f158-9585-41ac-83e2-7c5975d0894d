from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status
from datetime import datetime
import json

def validate_field_type(field_name: str, field_value: Any, expected_type: str) -> bool:
    """
    验证字段值是否符合预期类型
    """
    if field_value is None:
        return True

    if expected_type == "string":
        return isinstance(field_value, str)
    elif expected_type == "number":
        return isinstance(field_value, (int, float))
    elif expected_type == "boolean":
        return isinstance(field_value, bool)
    elif expected_type == "array":
        return isinstance(field_value, list)
    elif expected_type == "date":
        # 日期可以是字符串或datetime对象
        if isinstance(field_value, datetime):
            return True
        if isinstance(field_value, str):
            try:
                datetime.fromisoformat(field_value.replace('Z', '+00:00'))
                return True
            except ValueError:
                return False
        return False
    elif expected_type == "select":
        # 单选类型应该是一个包含_type和value的字典
        if not isinstance(field_value, dict):
            return False
        if field_value.get("_type") != "select":
            return False
        # 检查选项是否有效
        if "options" in field_value and not isinstance(field_value["options"], list):
            return False
        return True
    elif expected_type == "multiselect":
        # 多选类型应该是一个包含_type和value的字典，value是一个列表
        if not isinstance(field_value, dict):
            return False
        if field_value.get("_type") != "multiselect":
            return False
        if "value" in field_value and not isinstance(field_value["value"], list):
            return False
        # 检查选项是否有效
        if "options" in field_value and not isinstance(field_value["options"], list):
            return False
        return True

    # 未知类型
    return False

def validate_fields(fields: Dict[str, Any]) -> List[str]:
    """
    验证字段定义和值
    返回错误消息列表，如果没有错误则返回空列表
    """
    errors = []

    for field_name, field_value in fields.items():
        # 检查字段名称
        if not field_name or not isinstance(field_name, str):
            errors.append(f"字段名称必须是非空字符串")
            continue

        # 检查字段值类型
        if isinstance(field_value, dict) and "_type" in field_value:
            # 对于复杂类型（单选、多选等）
            field_type = field_value.get("_type")
            if not validate_field_type(field_name, field_value, field_type):
                errors.append(f"字段 '{field_name}' 的值不符合类型 '{field_type}' 的要求")
        elif field_value is not None:
            # 对于基本类型，根据值的Python类型判断
            if isinstance(field_value, bool):
                field_type = "boolean"
            elif isinstance(field_value, (int, float)):
                field_type = "number"
            elif isinstance(field_value, list):
                field_type = "array"
            elif isinstance(field_value, str):
                field_type = "string"
            else:
                errors.append(f"字段 '{field_name}' 的值类型不受支持")
                continue

            if not validate_field_type(field_name, field_value, field_type):
                errors.append(f"字段 '{field_name}' 的值不符合类型 '{field_type}' 的要求")

    return errors

def validate_primary_key_exists(fields: Dict[str, Any]) -> bool:
    """
    验证是否存在商品条码或商品编号作为主键
    """
    # 检查是否有商品条码或商品编号字段
    primary_key_fields = ["商品条码", "商品编号", "产品编号", "产品条码", "条码", "编号", "barcode", "product_code", "product_id"]

    for field_name in fields:
        if field_name.lower() in [pk.lower() for pk in primary_key_fields]:
            return True

    # 检查字段描述或标签中是否包含主键信息
    for field_name, field_value in fields.items():
        if isinstance(field_value, dict) and "label" in field_value:
            label = field_value["label"]
            if isinstance(label, str) and any(pk.lower() in label.lower() for pk in primary_key_fields):
                return True

    return False

def validate_required_fields(fields: Dict[str, Any]) -> List[str]:
    """
    验证必填字段是否已填写
    """
    errors = []

    for field_name, field_value in fields.items():
        # 检查字段是否标记为必填
        is_required = False
        if isinstance(field_value, dict):
            is_required = field_value.get("required", False)

            # 如果是必填字段，检查是否有值
            if is_required:
                if "value" not in field_value or field_value["value"] is None or (isinstance(field_value["value"], str) and not field_value["value"].strip()):
                    errors.append(f"字段 '{field_name}' 是必填项")

    return errors

def validate_task_fields(fields: Dict[str, Any]) -> None:
    """
    验证任务字段，如果验证失败则抛出HTTPException
    """
    errors = validate_fields(fields)

    # 验证是否存在主键字段
    if not validate_primary_key_exists(fields):
        errors.append("任务必须包含商品条码或商品编号作为主键")

    # 验证必填字段
    required_errors = validate_required_fields(fields)
    errors.extend(required_errors)

    if errors:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={"errors": errors}
        )
