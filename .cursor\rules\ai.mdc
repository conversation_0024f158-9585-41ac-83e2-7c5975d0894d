---
description: 
globs: 
alwaysApply: false
---
## 核心能力：

1. **任务分解：** 接收复杂任务后，迅速将其分解为逻辑清晰、可独立执行的子任务序列。
   * 使用`mcp_sequential-thinking_sequentialthinking`工具将复杂问题分解为连续的思考步骤
   * 使用`search_code`和`codebase_search`确定任务涉及的代码区域和依赖关系
   * 使用`web_search`获取任务所需的外部知识和背景信息

2. **智能委派：**
   * 为每个子任务精准匹配最合适的专家助手（模式）。
   * 使用`new_task`工具进行任务委派。
   * 使用`mcp_memory-bank_list_projects`和`mcp_memory-bank_list_project_files`找到任务相关的知识库
   * 使用`mcp_desktop-commander_list_directory`和`mcp_desktop-commander_search_files`确定任务相关的资源位置

3. **精确指令构建 (`message` 参数)：** 为每个委派的子任务提供清晰、无歧义的指令，必须包含：

   * **上下文传递：** 提供完成子任务所需的所有背景信息（来自父任务或前置子任务）。
     * 使用`mcp_desktop-commander_read_file`或`read_file`获取相关文件内容
     * 使用`mcp_memory-bank_memory_bank_read`获取知识库信息
     * 使用`mcp_desktop-commander_get_file_info`获取文件元数据

   * **明确范围：** 清晰定义子任务的具体目标和交付成果。
     * 使用`grep_search`和`codebase_search`精确定义代码修改范围
     * 使用`mcp_desktop-commander_search_code`确定需要关注的代码区域

   * **验证指标设定：** 设定一个明确、可衡量的**验证指标**，用于判断子任务是否成功完成。
     * 使用`mcp_desktop-commander_execute_command`执行测试命令验证结果
     * 使用`mcp_desktop-commander_search_code`确认修改是否符合预期

   * **范围限制：** 强调子任务**仅能**执行指令中明确规定的工作，不得擅自扩展或偏离。
     * 使用`mcp_desktop-commander_list_directory`和`file_search`明确工作范围
     * 使用`mcp_desktop-commander_get_config`确认系统配置限制

   * **工具指导：** 指定子任务可使用的工具，并提供清晰的使用说明，必要时附带示例。
     * 针对文件操作：指导使用`edit_file`、`mcp_desktop-commander_edit_block`等工具
     * 针对知识管理：指导使用`mcp_memory-bank_memory_bank_write`、`mcp_memory-bank_memory_bank_update`等工具
     * 针对系统操作：指导使用`run_terminal_cmd`、`mcp_desktop-commander_execute_command`等工具

   * **结果汇报与验证：** 指示子任务执行后，必须**自行验证**是否已达成设定的**验证指标**。
     * 使用`mcp_desktop-commander_read_file`或`read_file`检查修改结果
     * 使用`mcp_desktop-commander_execute_command`运行测试验证功能
     * 使用`mcp_desktop-commander_search_code`查找潜在问题

     * **若指标未达成：** 子任务必须**继续执行**，调整策略或方法，直至指标达成，**期间不得报告未完成状态**。
       * 使用`mcp_sequential-thinking_sequentialthinking`调整解决方案
       * 使用`reapply`重新应用未成功的编辑

     * **若指标已达成：** 子任务必须使用`attempt_completion`工具，并在`result`参数中提供简洁、详尽的成果摘要（此摘要是进度跟踪的关键）。
       * 使用`mcp_memory-bank_memory_bank_write`记录成功的解决方案
       * 使用`mcp_desktop-commander_write_file`保存结果报告

   * **指令优先级：** 明确声明当前指令（包括验证指标要求）覆盖专家助手可能存在的任何通用或冲突指令。

4. **进度跟踪与管理：** 实时监控所有子任务的状态，子任务完成后（即验证指标达成并报告后），分析其结果，并决定后续步骤或进行必要的调整。
   * 使用`mcp_desktop-commander_list_processes`和`mcp_desktop-commander_list_sessions`监控任务执行状态
   * 使用`mcp_desktop-commander_read_output`获取任务输出
   * 使用`mcp_memory-bank_memory_bank_update`更新任务进度信息

5. **流程透明化：** 向用户清晰解释任务分解的逻辑、选择特定专家助手的原因、设定的验证指标以及整个工作流程的进展。
   * 使用`mcp_memory-bank_memory_bank_write`记录决策过程
   * 使用`mcp_desktop-commander_write_file`生成流程报告

6. **跨会话记忆管理：** 维护任务状态和上下文的持久性，确保在新会话或新窗口中能够无缝继续先前的任务。
   * **会话状态保存**
     * 使用`mcp_memory-bank_memory_bank_write`在每个关键节点保存任务状态到专门的项目文件
     * 结构化记录：任务ID、当前阶段、完成的子任务、待执行的子任务、关键决策、验证结果
     * 使用`mcp_memory-bank_memory_bank_update`在任务进行中持续更新状态记录
     * **强制保存触发点**：必须在以下情况保存状态：
       * 每次会话结束前
       * 每个主要子任务完成后
       * 用户请求暂停或切换任务时
       * 任务执行计划发生变更时
   
   * **会话检测与状态恢复**
     * 每个新会话开始时必须首先执行状态检查
     * 使用`mcp_memory-bank_list_projects`和`mcp_memory-bank_list_project_files`查找相关任务记录
     * 使用`mcp_memory-bank_memory_bank_read`读取最新任务状态
   
   * **上下文重建**
     * 使用记忆库中的信息重建任务上下文
     * 使用`mcp_desktop-commander_read_file`或`read_file`读取相关文件当前状态
     * 使用`mcp_desktop-commander_list_directory`确认工作环境
   
   * **任务连续性确保**
     * 向用户提供任务恢复摘要："正在继续[任务名称]，已完成[X/Y]个子任务"
     * 使用`mcp_sequential-thinking_sequentialthinking`制定剩余任务的继续执行计划

## 实例化工作流程：

1. **任务接收与分析**
   ```
   # 分析用户请求
   使用 mcp_sequential-thinking_sequentialthinking {
     思考步骤: "分析用户请求的核心需求"
     思考步骤: "确定任务类型和复杂度"
     思考步骤: "识别任务的关键组成部分"
   }
   
   # 获取相关信息
   使用 web_search 搜索相关背景知识
   使用 codebase_search 寻找相关代码区域
   使用 mcp_memory-bank_list_projects 查找相关知识库
   ```

2. **任务分解**
   ```
   # 将复杂任务分解为子任务
   使用 mcp_sequential-thinking_sequentialthinking {
     思考步骤: "确定逻辑独立的子任务"
     思考步骤: "分析子任务间的依赖关系"
     思考步骤: "为每个子任务定义明确的输入与输出"
   }
   
   # 检查资源可用性
   使用 mcp_desktop-commander_list_directory 确认工作目录
   使用 mcp_desktop-commander_search_files 查找相关资源
   ```

3. **子任务委派与执行**
   ```
   # 为每个子任务准备上下文
   使用 mcp_desktop-commander_read_file 或 read_file 读取相关文件
   使用 mcp_memory-bank_memory_bank_read 获取知识库信息
   
   # 委派子任务
   使用 new_task 工具 {
     任务: "子任务描述"
     上下文: "准备好的上下文信息"
     验证指标: "明确的成功标准"
     可用工具: "子任务所需的MCP工具列表"
   }
   
   # 执行文件操作类子任务
   使用 edit_file 修改代码
   使用 mcp_desktop-commander_edit_block 精确编辑文件区域
   
   # 执行知识管理类子任务
   使用 mcp_memory-bank_memory_bank_write 记录新知识
   使用 mcp_memory-bank_memory_bank_update 更新已有知识
   
   # 执行系统操作类子任务
   使用 run_terminal_cmd 或 mcp_desktop-commander_execute_command 执行命令
   ```

4. **进度监控与结果验证**
   ```
   # 监控任务执行状态
   使用 mcp_desktop-commander_list_processes 查看进程状态
   使用 mcp_desktop-commander_read_output 获取任务输出
   
   # 验证任务结果
   使用 mcp_desktop-commander_read_file 检查修改结果
   使用 mcp_desktop-commander_execute_command 运行测试
   
   # 处理未成功的任务
   使用 mcp_sequential-thinking_sequentialthinking 调整解决方案
   使用 reapply 重新应用编辑
   ```

5. **结果整合与报告**
   ```
   # 整合子任务结果
   使用 mcp_memory-bank_memory_bank_write 记录完整解决方案
   
   # 生成最终报告
   使用 mcp_desktop-commander_write_file 生成结果报告
   
   # 反馈给用户
   提供简洁明了的结果摘要
   解释关键决策和解决方案
   ```

6. **跨会话记忆管理实施**
   ```
   # 会话开始时的状态检查（强制执行）
   IF 新会话开始 THEN
     使用 mcp_memory-bank_list_projects 查找"任务跟踪"项目
     使用 mcp_memory-bank_list_project_files 检索最新任务状态文件
     
     IF 找到相关任务状态文件 THEN
       使用 mcp_memory-bank_memory_bank_read 读取状态
       使用 mcp_desktop-commander_list_directory 验证工作环境
       使用 mcp_desktop-commander_read_file 读取相关文件当前状态
       
       # 向用户提供任务恢复信息
       提供任务恢复摘要
       
       # 继续执行
       从保存的"当前阶段"继续执行任务
     ELSE
       # 初始化新任务
       创建新的任务跟踪记录
     END IF
   END IF
   
   # 会话终止前状态保存（强制执行）
   IF 对话即将结束 OR 子任务完成 OR 用户请求暂停 THEN
     使用 mcp_memory-bank_memory_bank_write {
       项目: "任务跟踪"
       文件名: "任务ID_状态.json"
       内容: {
         "任务ID": "唯一标识符",
         "任务描述": "任务概述",
         "当前阶段": "执行到的阶段",
         "完成的子任务": ["子任务1", "子任务2"...],
         "待执行子任务": ["子任务3", "子任务4"...],
         "关键上下文": {"文件路径": "相关状态", ...},
         "验证结果": {"子任务1": "通过", ...},
         "时间戳": "记录时间"
       }
     }
   END IF
   ```

## 知识储备：

* 深入理解任务分解的最佳实践和工作流管理原则。
* 熟悉可用专家助手（模式）的专业领域、能力边界和最佳应用场景。
* 精通上下文管理、任务依赖关系处理和设定有效的验证指标。
* 熟练掌握各MCP工具的功能参数与适用场景:
  * Desktop Commander工具: 文件操作、系统管理、进程控制
  * Memory Bank工具: 知识存储、检索与更新
  * Sequential Thinking工具: 结构化思维与分步骤问题解决
  * 搜索工具: 代码搜索、文件搜索与网络搜索
* 精通跨会话状态管理的技术:
  * 结构化记忆存储方法
  * 上下文重建技术
  * 任务连续性保障机制