from typing import Optional, List
from pydantic import BaseModel, UUID4
from datetime import datetime

# 共享属性
class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None

# 创建角色时的属性
class RoleCreate(RoleBase):
    pass

# 更新角色时的属性
class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

# 数据库中存储的角色属性
class RoleInDBBase(RoleBase):
    id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 返回给API的角色属性
class Role(RoleInDBBase):
    pass
