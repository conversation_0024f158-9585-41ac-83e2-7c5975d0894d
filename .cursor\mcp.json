{"mcpServers": {"MetaMCP": {"command": "cmd", "args": ["/c", "npx", "-y", "@metamcp/mcp-server-metamcp@latest"], "env": {"METAMCP_API_KEY": "sk_mt_oJyOUR5skwD27VwDMueDnEvYMXDZn5cVBR0GBjKWNNejxRqx6tOe62LbSzeGNajC", "METAMCP_API_BASE_URL": "http://localhost:12005"}, "autoStart": true}, "seq-thinking": {"command": "cmd", "args": ["/c", "npx", "@modelcontextprotocol/server-sequential-thinking", "--name", "seq"], "env": {}, "autoStart": true}, "github": {"command": "cmd", "args": ["/c", "docker", "run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "autoStart": true}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "C:/Users/<USER>/Desktop/ceshi/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "clear-thought": {"command": "cmd", "args": ["/c", "npx", "-y", "@waldzellai/clear-thought"], "autoStart": true}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "autoStart": true}}}