from typing import Optional, List
from pydantic import BaseModel, UUID4
from datetime import datetime

# 共享属性
class PermissionBase(BaseModel):
    name: str
    description: Optional[str] = None
    resource: str
    action: str

# 创建权限时的属性
class PermissionCreate(PermissionBase):
    pass

# 更新权限时的属性
class PermissionUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    resource: Optional[str] = None
    action: Optional[str] = None

# 数据库中存储的权限属性
class PermissionInDBBase(PermissionBase):
    id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 返回给API的权限属性
class Permission(PermissionInDBBase):
    pass
