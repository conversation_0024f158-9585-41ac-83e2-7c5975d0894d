from sqlalchemy.orm import Session

from app.db.session import engine
from app.models.base import BaseModel
from app.models.user import User
from app.models.company import Company
from app.models.task import Task
from app.models.quote import Quote
from app.models.role import Role
from app.models.permission import Permission
from app.models.system_config import SystemConfig
from app.core.security import get_password_hash
from app.db.init_permissions import init_roles_and_permissions

# 创建数据库表
def init_db():
    BaseModel.metadata.create_all(bind=engine)

    # 可以在这里添加初始数据
    # 例如创建一个管理员用户
    db = Session(engine)

    # 检查是否已存在管理员用户
    admin = db.query(User).filter(User.username == "admin").first()
    if not admin:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin"),
            name="管理员",
            level=4,  # 总部管理员
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        print("已创建管理员用户: admin/admin")

    # 初始化角色和权限
    init_roles_and_permissions(db)

    # 初始化默认系统配置
    supplier_verify_config = db.query(SystemConfig).filter(SystemConfig.key == "supplier_need_verify").first()
    if not supplier_verify_config:
        default_config = SystemConfig(
            key="supplier_need_verify",
            value={"enabled": False},
            description="是否需要审核新注册的供应商。true表示需要审核，false表示自动通过"
        )
        db.add(default_config)
        db.commit()
        print("已创建默认系统配置: supplier_need_verify = false")

    db.close()
