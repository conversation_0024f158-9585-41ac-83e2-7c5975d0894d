from sqlalchemy import Column, DateTime, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func
import uuid

from app.db.session import Base

class BaseModel(Base):
    """基础模型类，所有其他模型都应该继承这个类"""
    
    __abstract__ = True
    
    # 所有表名使用小写
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()
    
    # 通用字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
