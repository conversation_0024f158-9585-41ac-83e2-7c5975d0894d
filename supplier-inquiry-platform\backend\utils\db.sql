-- 创建数据库
CREATE DATABASE supplier_inquiry;

-- 连接到数据库
\c supplier_inquiry;

-- 创建用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(100) NOT NULL,
  name VARCHAR(50),
  email VARCHAR(100) NOT NULL UNIQUE,
  phone VARCHAR(20),
  company_id UUID,
  parent_id UUID REFERENCES users(id),
  level INTEGER NOT NULL, -- 1: 普通用户, 2: 部门主管, 3: 分公司负责人, 4: 总部管理员
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建公司表
CREATE TABLE companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  address TEXT,
  contact_person VARCHAR(50),
  contact_phone VARCHAR(20),
  contact_email VARCHAR(100),
  is_supplier BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE users ADD CONSTRAINT fk_company FOREIGN KEY (company_id) REFERENCES companies(id);

-- 创建任务表
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(100) NOT NULL,
  description TEXT,
  creator_id UUID REFERENCES users(id) NOT NULL,
  company_id UUID REFERENCES companies(id),
  fields JSONB NOT NULL,
  deadline TIMESTAMP,
  allow_guest BOOLEAN DEFAULT true,
  status VARCHAR(20) DEFAULT 'draft', -- draft, active, closed
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建报价表
CREATE TABLE quotes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES tasks(id) NOT NULL,
  supplier_id UUID REFERENCES companies(id),
  supplier_name VARCHAR(100) NOT NULL,
  data JSONB NOT NULL,
  attachments JSONB,
  is_registered BOOLEAN DEFAULT false,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_level ON users(level);
CREATE INDEX idx_tasks_creator ON tasks(creator_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_quotes_task ON quotes(task_id);
CREATE INDEX idx_quotes_supplier ON quotes(supplier_id);
