from typing import Optional, List
from pydantic import BaseModel, EmailStr, UUID4
from datetime import datetime

# 共享属性
class UserBase(BaseModel):
    username: str
    email: EmailStr
    name: Optional[str] = None
    phone: Optional[str] = None
    level: int = 1
    is_active: bool = True

# 创建用户时的属性
class UserCreate(UserBase):
    password: str
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None

# 更新用户时的属性
class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None
    level: Optional[int] = None
    is_active: Optional[bool] = None

# 数据库中存储的用户属性
class UserInDBBase(UserBase):
    id: UUID4
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 返回给API的用户属性
class User(UserInDBBase):
    pass

# 数据库中存储的用户属性（包含密码）
class UserInDB(UserInDBBase):
    hashed_password: str
