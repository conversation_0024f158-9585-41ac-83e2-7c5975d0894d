from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, UUID4
from app.schemas.user import User

class TaskBase(BaseModel):
    """任务基础模型"""
    title: str
    description: Optional[str] = None
    fields: Dict[str, Any]
    deadline: Optional[datetime] = None
    allow_guest: Optional[bool] = True
    status: Optional[str] = "draft"  # draft, active, closed
    publish_time: Optional[datetime] = None

class TaskCreate(TaskBase):
    """创建任务模型"""
    company_id: Optional[UUID4] = None

class TaskUpdate(BaseModel):
    """更新任务模型"""
    title: Optional[str] = None
    description: Optional[str] = None
    fields: Optional[Dict[str, Any]] = None
    deadline: Optional[datetime] = None
    allow_guest: Optional[bool] = None
    status: Optional[str] = None
    company_id: Optional[UUID4] = None
    publish_time: Optional[datetime] = None

class TaskInDBBase(TaskBase):
    """数据库中的任务基础模型"""
    id: UUID4
    creator_id: UUID4
    company_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Task(TaskInDBBase):
    """返回给API的任务模型"""
    creator: Optional[User] = None
