{"version": 3, "sources": ["../../file-saver/src/FileSaver.js"], "sourcesContent": ["/*\n* FileSaver.js\n* A saveAs() FileSaver implementation.\n*\n* By <PERSON>, http://eligrey.com\n*\n* License : https://github.com/eligrey/FileSaver.js/blob/master/LICENSE.md (MIT)\n* source  : http://purl.eligrey.com/github/FileSaver.js\n*/\n\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nvar _global = typeof window === 'object' && window.window === window\n  ? window : typeof self === 'object' && self.self === self\n  ? self : typeof global === 'object' && global.global === global\n  ? global\n  : this\n\nfunction bom (blob, opts) {\n  if (typeof opts === 'undefined') opts = { autoBom: false }\n  else if (typeof opts !== 'object') {\n    console.warn('Deprecated: Expected third argument to be a object')\n    opts = { autoBom: !opts }\n  }\n\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (opts.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xFEFF), blob], { type: blob.type })\n  }\n  return blob\n}\n\nfunction download (url, name, opts) {\n  var xhr = new XMLHttpRequest()\n  xhr.open('GET', url)\n  xhr.responseType = 'blob'\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts)\n  }\n  xhr.onerror = function () {\n    console.error('could not download file')\n  }\n  xhr.send()\n}\n\nfunction corsEnabled (url) {\n  var xhr = new XMLHttpRequest()\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false)\n  try {\n    xhr.send()\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299\n}\n\n// `a.click()` doesn't work for all browsers (#465)\nfunction click (node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'))\n  } catch (e) {\n    var evt = document.createEvent('MouseEvents')\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80,\n                          20, false, false, false, false, 0, null)\n    node.dispatchEvent(evt)\n  }\n}\n\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nvar isMacOSWebView = _global.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent)\n\nvar saveAs = _global.saveAs || (\n  // probably in some web worker\n  (typeof window !== 'object' || window !== _global)\n    ? function saveAs () { /* noop */ }\n\n  // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView\n  : ('download' in HTMLAnchorElement.prototype && !isMacOSWebView)\n  ? function saveAs (blob, name, opts) {\n    var URL = _global.URL || _global.webkitURL\n    var a = document.createElement('a')\n    name = name || blob.name || 'download'\n\n    a.download = name\n    a.rel = 'noopener' // tabnabbing\n\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n\n    if (typeof blob === 'string') {\n      // Support regular links\n      a.href = blob\n      if (a.origin !== location.origin) {\n        corsEnabled(a.href)\n          ? download(blob, name, opts)\n          : click(a, a.target = '_blank')\n      } else {\n        click(a)\n      }\n    } else {\n      // Support blobs\n      a.href = URL.createObjectURL(blob)\n      setTimeout(function () { URL.revokeObjectURL(a.href) }, 4E4) // 40s\n      setTimeout(function () { click(a) }, 0)\n    }\n  }\n\n  // Use msSaveOrOpenBlob as a second approach\n  : 'msSaveOrOpenBlob' in navigator\n  ? function saveAs (blob, name, opts) {\n    name = name || blob.name || 'download'\n\n    if (typeof blob === 'string') {\n      if (corsEnabled(blob)) {\n        download(blob, name, opts)\n      } else {\n        var a = document.createElement('a')\n        a.href = blob\n        a.target = '_blank'\n        setTimeout(function () { click(a) })\n      }\n    } else {\n      navigator.msSaveOrOpenBlob(bom(blob, opts), name)\n    }\n  }\n\n  // Fallback to using FileReader and a popup\n  : function saveAs (blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank')\n    if (popup) {\n      popup.document.title =\n      popup.document.body.innerText = 'downloading...'\n    }\n\n    if (typeof blob === 'string') return download(blob, name, opts)\n\n    var force = blob.type === 'application/octet-stream'\n    var isSafari = /constructor/i.test(_global.HTMLElement) || _global.safari\n    var isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent)\n\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) && typeof FileReader !== 'undefined') {\n      // Safari doesn't allow downloading of blob URLs\n      var reader = new FileReader()\n      reader.onloadend = function () {\n        var url = reader.result\n        url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;')\n        if (popup) popup.location.href = url\n        else location = url\n        popup = null // reverse-tabnabbing #460\n      }\n      reader.readAsDataURL(blob)\n    } else {\n      var URL = _global.URL || _global.webkitURL\n      var url = URL.createObjectURL(blob)\n      if (popup) popup.location = url\n      else location.href = url\n      popup = null // reverse-tabnabbing #460\n      setTimeout(function () { URL.revokeObjectURL(url) }, 4E4) // 40s\n    }\n  }\n)\n\n_global.saveAs = saveAs.saveAs = saveAs\n\nif (typeof module !== 'undefined') {\n  module.exports = saveAs;\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAkBA,eAAS,EAAKA,IAAMC,IAAM;AAAA,eACJ,eAAhB,OAAOA,KAAsBA,KAAO,EAAE,SAAO,MAAT,IACf,YAAhB,OAAOA,OACd,QAAQ,KAAK,oDAAb,GACAA,KAAO,EAAE,SAAS,CAACA,GAAZ,IAKLA,GAAK,WAAW,6EAA6E,KAAKD,GAAK,IAAvF,IACX,IAAI,KAAK,CAAA,UAA8BA,EAA9B,GAAqC,EAAE,MAAMA,GAAK,KAAb,CAA9C,IAEFA;MACR;AAED,eAAS,EAAUA,IAAKC,IAAMC,IAAM;AAClC,YAAIC,KAAM,IAAI;AACd,QAAAA,GAAI,KAAK,OAAOH,EAAhB,GACAG,GAAI,eAAe,QACnBA,GAAI,SAAS,WAAY;AACvB,YAAOA,GAAI,UAAUF,IAAMC,EAArB;QACP,GACDC,GAAI,UAAU,WAAY;AACxB,kBAAQ,MAAM,yBAAd;QACD,GACDA,GAAI,KAAJ;MACD;AAED,eAAS,EAAaH,IAAK;AACzB,YAAIC,KAAM,IAAI;AAEd,QAAAA,GAAI,KAAK,QAAQD,IAAjB,KAAA;AACA,YAAI;AACF,UAAAC,GAAI,KAAJ;QACD,SAAQD,IAAG;QAAE;AACd,eAAqB,OAAdC,GAAI,UAA+B,OAAdA,GAAI;MACjC;AAGD,eAAS,EAAOD,IAAM;AACpB,YAAI;AACF,UAAAA,GAAK,cAAc,IAAI,WAAW,OAAf,CAAnB;QACD,SAAQE,IAAG;AACV,cAAID,KAAM,SAAS,YAAY,aAArB;AACV,UAAAA,GAAI,eAAe,SAAnB,MAAA,MAAwC,QAAQ,GAAG,GAAG,GAAG,IACnC,IADtB,OAAA,OAAA,OAAA,OACsD,GAAG,IADzD,GAEAD,GAAK,cAAcC,EAAnB;QACD;MACF;AAAA,UAtDG,IAA4B,YAAlB,OAAO,UAAuB,OAAO,WAAW,SAC1D,SAAyB,YAAhB,OAAO,QAAqB,KAAK,SAAS,OACnD,OAAyB,YAAlB,OAAO,UAAuB,OAAO,WAAW,SACvD,SADO,QAyDP,IAAiB,EAAQ,aAAa,YAAY,KAAK,UAAU,SAA3B,KAAyC,cAAc,KAAK,UAAU,SAA7B,KAA2C,CAAC,SAAS,KAAK,UAAU,SAAxB,GAE3H,IAAS,EAAQ,WAEA,YAAlB,OAAO,UAAuB,WAAW,IACtC,WAAmB;MAAc,IAGlC,cAAc,kBAAkB,aAAa,CAAC,IAC/C,SAAiBA,IAAMG,IAAM,GAAM;AAAA,YAC/B,IAAM,EAAQ,OAAO,EAAQ,WAC7B,IAAI,SAAS,cAAc,GAAvB;AACR,QAAAA,KAAOA,MAAQH,GAAK,QAAQ,YAE5B,EAAE,WAAWG,IACb,EAAE,MAAM,YAKY,YAAhB,OAAOH,MAET,EAAE,OAAOA,IACL,EAAE,WAAW,SAAS,SAKxB,EAAM,CAAD,IAJL,EAAY,EAAE,IAAH,IACP,EAASA,IAAMG,IAAM,CAAb,IACR,EAAM,GAAG,EAAE,SAAS,QAAf,MAMX,EAAE,OAAO,EAAI,gBAAgBH,EAApB,GACT,WAAW,WAAY;AAAE,YAAI,gBAAgB,EAAE,IAAtB;QAA6B,GAAE,GAA9C,GACV,WAAW,WAAY;AAAE,YAAM,CAAD;QAAK,GAAE,CAA3B;MAEb,IAGC,sBAAsB,YACtB,SAAiBI,IAAMD,IAAM,GAAM;AAGnC,YAFAA,KAAOA,MAAQC,GAAK,QAAQ,YAER,YAAhB,OAAOA,GAUT,WAAU,iBAAiB,EAAIA,IAAM,CAAP,GAAcD,EAA5C;iBATI,EAAYC,EAAD,EACb,GAASA,IAAMD,IAAM,CAAb;aACH;AACL,cAAI,IAAI,SAAS,cAAc,GAAvB;AACR,YAAE,OAAOC,IACT,EAAE,SAAS,UACX,WAAW,WAAY;AAAE,cAAM,CAAD;UAAK,CAAzB;QACX;MAIJ,IAGC,SAAiBJ,IAAME,IAAMG,IAAMF,IAAO;AAS1C,YANAA,KAAQA,MAAS,KAAK,IAAI,QAAL,GACjBA,OACFA,GAAM,SAAS,QACfA,GAAM,SAAS,KAAK,YAAY,mBAGd,YAAhB,OAAOH,GAAmB,QAAO,EAASA,IAAME,IAAMG,EAAb;AATH,YAWtC,IAAsB,+BAAdL,GAAK,MACb,IAAW,eAAe,KAAK,EAAQ,WAA5B,KAA4C,EAAQ,QAC/D,IAAc,eAAe,KAAK,UAAU,SAA9B;AAElB,aAAK,KAAgB,KAAS,KAAa,MAAyC,eAAtB,OAAO,YAA4B;AAE/F,cAAI,IAAS,IAAI;AACjB,YAAO,YAAY,WAAY;AAC7B,gBAAID,KAAM,EAAO;AACjB,YAAAA,KAAM,IAAcA,KAAMA,GAAI,QAAQ,gBAAgB,uBAA5B,GACtBI,KAAOA,GAAM,SAAS,OAAOJ,KAC5B,WAAWA,IAChBI,KAAQ;UACT,GACD,EAAO,cAAcH,EAArB;QACD,OAAM;AAAA,cACD,IAAM,EAAQ,OAAO,EAAQ,WAC7B,IAAM,EAAI,gBAAgBA,EAApB;AACN,UAAAG,KAAOA,GAAM,WAAW,IACvB,SAAS,OAAO,GACrBA,KAAQ,MACR,WAAW,WAAY;AAAE,cAAI,gBAAgB,CAApB;UAA0B,GAAE,GAA3C;QACX;MACF;AAGH,QAAQ,SAAS,EAAO,SAAS,GAEX,eAAlB,OAAO,WACT,OAAO,UAAU;IAAA,CAAA;;;", "names": ["a", "b", "c", "d", "g", "f", "e"]}