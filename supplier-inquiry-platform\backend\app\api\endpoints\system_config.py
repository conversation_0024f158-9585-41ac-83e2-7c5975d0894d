from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.system_config import SystemConfig
from app.schemas.system_config import (
    SystemConfig as SystemConfigSchema,
    SystemConfigCreate,
    SystemConfigUpdate
)

router = APIRouter()

@router.get("/public/registration-config")
def get_public_registration_config(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取注册相关的公开配置（无需认证）
    用于注册页面显示审核提示信息
    """
    # 查询供应商审核配置
    supplier_verify_config = db.query(SystemConfig).filter(
        SystemConfig.key == "supplier_need_verify"
    ).first()

    # 默认配置：不需要审核
    need_verify = False
    if supplier_verify_config and supplier_verify_config.value:
        need_verify = supplier_verify_config.value.get("enabled", False)

    return {
        "supplier_need_verify": need_verify,
        "message": "注册后需要管理员审核才能使用" if need_verify else "注册后可立即使用"
    }

@router.get("/", response_model=List[SystemConfigSchema])
def read_system_configs(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取系统配置列表（仅限管理员）
    """
    # 权限检查：只有level>=4的管理员可以访问
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    configs = db.query(SystemConfig).offset(skip).limit(limit).all()
    return configs

@router.get("/{config_key}", response_model=SystemConfigSchema)
def read_system_config(
    config_key: str,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    根据配置键获取系统配置（仅限管理员）
    """
    # 权限检查：只有level>=4的管理员可以访问
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    config = db.query(SystemConfig).filter(SystemConfig.key == config_key).first()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置项不存在",
        )

    return config

@router.post("/", response_model=SystemConfigSchema)
def create_system_config(
    *,
    db: Session = Depends(deps.get_db),
    config_in: SystemConfigCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新的系统配置（仅限管理员）
    """
    # 权限检查：只有level>=4的管理员可以访问
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 检查配置键是否已存在
    existing_config = db.query(SystemConfig).filter(SystemConfig.key == config_in.key).first()
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置键已存在",
        )

    # 创建新配置
    config = SystemConfig(**config_in.dict())
    db.add(config)
    db.commit()
    db.refresh(config)
    return config

@router.put("/{config_key}", response_model=SystemConfigSchema)
def update_system_config(
    *,
    db: Session = Depends(deps.get_db),
    config_key: str,
    config_in: SystemConfigUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新系统配置（仅限管理员）
    """
    # 权限检查：只有level>=4的管理员可以访问
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    config = db.query(SystemConfig).filter(SystemConfig.key == config_key).first()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置项不存在",
        )

    # 更新配置信息
    update_data = config_in.dict(exclude_unset=True)
    for field in update_data:
        setattr(config, field, update_data[field])

    db.add(config)
    db.commit()
    db.refresh(config)
    return config

@router.delete("/{config_key}", response_model=SystemConfigSchema)
def delete_system_config(
    *,
    db: Session = Depends(deps.get_db),
    config_key: str,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除系统配置（仅限管理员）
    """
    # 权限检查：只有level>=4的管理员可以访问
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    config = db.query(SystemConfig).filter(SystemConfig.key == config_key).first()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置项不存在",
        )

    db.delete(config)
    db.commit()
    return config