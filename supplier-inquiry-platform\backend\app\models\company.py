from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID

class Company(BaseModel):
    """公司模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    address = Column(String)
    contact_person = Column(String)
    contact_phone = Column(String)
    contact_email = Column(String)
    is_supplier = Column(Boolean, default=False)
    is_blacklisted = Column(Boolean, default=False)  # 是否黑名单
    is_verified = Column(Boolean, default=True)      # 是否已审核

    # 关系
    users = relationship("User", back_populates="company")
    tasks = relationship("Task", back_populates="company")
    quotes = relationship("Quote", back_populates="supplier")
