import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
// [times, realValue]

var SPLIT = '%';

/** Connect key with `SPLIT` */
export function pathKey(keys) {
  return keys.join(SPLIT);
}
var Entity = /*#__PURE__*/function () {
  function Entity(instanceId) {
    _classCallCheck(this, Entity);
    _defineProperty(this, "instanceId", void 0);
    /** @private Internal cache map. Do not access this directly */
    _defineProperty(this, "cache", new Map());
    this.instanceId = instanceId;
  }
  _createClass(Entity, [{
    key: "get",
    value: function get(keys) {
      return this.opGet(pathKey(keys));
    }

    /** A fast get cache with `get` concat. */
  }, {
    key: "opGet",
    value: function opGet(keyPathStr) {
      return this.cache.get(keyPathStr) || null;
    }
  }, {
    key: "update",
    value: function update(keys, valueFn) {
      return this.opUpdate(pathKey(keys), valueFn);
    }

    /** A fast get cache with `get` concat. */
  }, {
    key: "opUpdate",
    value: function opUpdate(keyPathStr, valueFn) {
      var prevValue = this.cache.get(keyPathStr);
      var nextValue = valueFn(prevValue);
      if (nextValue === null) {
        this.cache.delete(keyPathStr);
      } else {
        this.cache.set(keyPathStr, nextValue);
      }
    }
  }]);
  return Entity;
}();
export default Entity;