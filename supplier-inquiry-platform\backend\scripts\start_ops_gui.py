#!/usr/bin/env python3
"""
供应商询价平台 - 运维管理工具启动脚本
===============================================
功能：检查依赖并启动GUI运维工具
作者：运维团队
版本：1.0
创建日期：2025-05-24
"""

import sys
import subprocess
import importlib
from pathlib import Path

def check_and_install_dependencies():
    """检查并安装依赖包"""
    required_packages = {
        'tkinter': 'tkinter',  # 通常是内置的
        'psutil': 'psutil',
        'requests': 'requests'
    }
    
    missing_packages = []
    
    print("检查依赖包...")
    
    for package_name, pip_name in required_packages.items():
        try:
            if package_name == 'tkinter':
                import tkinter
            else:
                importlib.import_module(package_name)
            print(f"✅ {package_name} - 已安装")
        except ImportError:
            print(f"❌ {package_name} - 未安装")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        
        try:
            for package in missing_packages:
                print(f"正在安装 {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装依赖失败: {e}")
            print("\n请手动运行以下命令安装依赖:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("    供应商询价平台 - 运维管理工具启动器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("\n❌ 依赖检查失败，程序退出")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("启动运维管理GUI工具...")
    print("=" * 60)
    
    try:
        # 导入并启动GUI工具
        from ops_manager_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保 ops_manager_gui.py 在同一目录下")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动GUI工具失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()