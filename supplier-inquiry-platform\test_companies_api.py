#!/usr/bin/env python3
"""
测试供应商API的简单脚本
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.db.session import SessionLocal
from app.models.company import Company

def test_database_companies():
    """测试数据库中的供应商数据"""
    db = SessionLocal()
    
    try:
        # 查询所有供应商
        suppliers = db.query(Company).filter(Company.is_supplier == True).all()
        
        print(f"数据库中共有 {len(suppliers)} 个供应商:")
        print("-" * 80)
        
        for supplier in suppliers:
            status = "正常"
            if supplier.is_blacklisted:
                status = "黑名单"
            elif not supplier.is_verified:
                status = "待审核"
            
            print(f"名称: {supplier.name}")
            print(f"联系人: {supplier.contact_person or 'N/A'}")
            print(f"电话: {supplier.contact_phone or 'N/A'}")
            print(f"状态: {status}")
            print("-" * 40)
            
    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    test_database_companies()
