from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import uuid

from app.api import deps
from app.models.role import Role
from app.models.permission import Permission
from app.models.user import User
from app.schemas.role import Role as RoleSchema, RoleCreate, RoleUpdate
from app.schemas.permission import Permission as PermissionSchema

router = APIRouter()

@router.get("/", response_model=List[RoleSchema])
def read_roles(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取角色列表
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    roles = db.query(Role).offset(skip).limit(limit).all()
    return roles

@router.get("/{role_id}", response_model=RoleSchema)
def read_role(
    role_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取角色详情
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    return role

@router.post("/", response_model=RoleSchema)
def create_role(
    *,
    db: Session = Depends(deps.get_db),
    role_in: RoleCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新角色
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "create", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    # 检查角色名是否已存在
    role = db.query(Role).filter(Role.name == role_in.name).first()
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名已存在",
        )
    
    # 创建新角色
    role = Role(**role_in.dict())
    db.add(role)
    db.commit()
    db.refresh(role)
    return role

@router.put("/{role_id}", response_model=RoleSchema)
def update_role(
    *,
    db: Session = Depends(deps.get_db),
    role_id: uuid.UUID,
    role_in: RoleUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新角色
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    
    # 更新角色
    update_data = role_in.dict(exclude_unset=True)
    for field in update_data:
        setattr(role, field, update_data[field])
    
    db.add(role)
    db.commit()
    db.refresh(role)
    return role

@router.delete("/{role_id}", response_model=RoleSchema)
def delete_role(
    *,
    db: Session = Depends(deps.get_db),
    role_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除角色
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "delete", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    
    # 检查是否是默认角色
    if role.name in ["basic_user", "department_manager", "branch_manager", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除默认角色",
        )
    
    db.delete(role)
    db.commit()
    return role

@router.post("/{role_id}/permissions")
def assign_permissions_to_role(
    *,
    db: Session = Depends(deps.get_db),
    role_id: uuid.UUID,
    permission_ids: List[uuid.UUID],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    分配权限给角色
    """
    # 权限检查
    if not deps.check_permission(current_user, "role", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    
    permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
    role.permissions = permissions
    
    db.add(role)
    db.commit()
    db.refresh(role)
    return {"message": "权限分配成功"}
