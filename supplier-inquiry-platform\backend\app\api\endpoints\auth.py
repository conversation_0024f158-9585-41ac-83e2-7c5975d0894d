from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.core.security import create_access_token, get_password_hash, verify_password
from app.models.user import User
from app.schemas.token import Token
from app.schemas.user import UserCreate, User as UserSchema
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.post("/login", response_model=Token)
def login_access_token(
    request: Request,
    db: Session = Depends(deps.get_db), 
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 兼容的token登录，获取访问token
    """
    logger.info(f"用户尝试登录: {form_data.username}")
    
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        logger.warning(f"登录失败: 用户名或密码错误 - 用户名: {form_data.username}, IP: {request.client.host}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        logger.warning(f"登录失败: 用户已被禁用 - 用户名: {form_data.username}, IP: {request.client.host}")
        raise HTTPException(status_code=400, detail="用户已被禁用")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token = create_access_token(user.id, expires_delta=access_token_expires)
    
    logger.info(f"用户登录成功: {user.username}, ID: {user.id}, IP: {request.client.host}")
    
    return {
        "access_token": token,
        "token_type": "bearer",
    }

@router.post("/register", response_model=UserSchema)
def register_user(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
) -> Any:
    """
    注册新用户
    """
    logger.info(f"用户注册请求: {user_in.username}, 邮箱: {user_in.email}")
    
    # 检查用户名是否已存在
    user = db.query(User).filter(User.username == user_in.username).first()
    if user:
        logger.warning(f"注册失败: 用户名已存在 - {user_in.username}, IP: {request.client.host}")
        raise HTTPException(
            status_code=400,
            detail="用户名已存在",
        )
    # 检查邮箱是否已存在
    user = db.query(User).filter(User.email == user_in.email).first()
    if user:
        logger.warning(f"注册失败: 邮箱已存在 - {user_in.email}, IP: {request.client.host}")
        raise HTTPException(
            status_code=400,
            detail="邮箱已存在",
        )
    # 创建新用户
    user = User(
        username=user_in.username,
        email=user_in.email,
        hashed_password=get_password_hash(user_in.password),
        name=user_in.name,
        phone=user_in.phone,
        company_id=user_in.company_id,
        parent_id=user_in.parent_id,
        level=user_in.level,
        is_active=user_in.is_active,
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    
    logger.info(f"用户注册成功: {user.username}, ID: {user.id}, IP: {request.client.host}")
    
    return user

@router.get("/me", response_model=UserSchema)
def read_users_me(
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户信息
    """
    logger.debug(f"用户请求个人信息: ID: {current_user.id}, 用户名: {current_user.username}")
    return current_user

@router.post("/logout")
def logout(request: Request) -> Any:
    """
    登出

    注意：由于JWT是无状态的，服务器端无法使令牌失效。
    客户端应该删除本地存储的令牌。
    """
    # 获取客户端IP
    client_ip = request.client.host
    logger.info(f"用户登出, IP: {client_ip}")
    return {"detail": "登出成功"}
