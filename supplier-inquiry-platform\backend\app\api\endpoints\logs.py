from fastapi import APIRouter, Depends, HTTPException, Body, Request
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.core.logging import get_logger
from app.api import deps

router = APIRouter()
logger = get_logger(__name__)

@router.post("/client-error")
async def log_client_error(
    request: Request,
    error_data: Dict[str, Any] = Body(...),
    db: Session = Depends(deps.get_db),
):
    """
    记录前端客户端错误
    """
    # 获取请求ID
    request_id = getattr(request.state, "request_id", "unknown")
    
    # 提取用户信息（如果已登录）
    user_id = "anonymous"
    try:
        # 尝试获取当前用户
        current_user = await deps.get_current_user_optional(db=db, request=request)
        if current_user:
            user_id = str(current_user.id)
    except Exception as e:
        logger.warning(f"从请求获取用户信息失败: {str(e)}")
        # 继续处理，使用anonymous用户ID
    
    # 记录错误
    level_name = error_data.get("level", "ERROR")
    message = error_data.get("message", "Client error")
    
    log_message = f"Client error: {message} | user_id={user_id} request_id={request_id}"
    
    # 创建安全的extra数据，避免与LogRecord内置字段冲突
    safe_extra = {}
    for key, value in error_data.items():
        # 避免与LogRecord内置字段冲突的字段名
        if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
                       'module', 'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
                       'thread', 'threadName', 'processName', 'process', 'message', 'exc_info',
                       'exc_text', 'stack_info']:
            safe_extra[f"client_{key}"] = value
    
    # 添加自定义字段
    safe_extra.update({
        "client_user_id": user_id,
        "client_request_id": request_id,
        "client_error_message": message
    })
    
    if level_name == "CRITICAL":
        logger.critical(log_message, extra=safe_extra)
    else:
        logger.error(log_message, extra=safe_extra)
    
    # 这里可以选择将错误保存到数据库
    # ...
    
    return {"status": "logged"} 