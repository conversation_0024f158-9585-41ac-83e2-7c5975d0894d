#!/usr/bin/env python3
"""
初始化审核配置脚本
创建用户和供应商审核相关的系统配置
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from app.db.session import SessionLocal
from app.models.system_config import SystemConfig

def init_audit_configs():
    """初始化审核配置"""
    db = SessionLocal()
    
    try:
        # 用户注册审核配置
        user_verify_config = db.query(SystemConfig).filter(
            SystemConfig.key == "user_need_verify"
        ).first()
        
        if not user_verify_config:
            user_verify_config = SystemConfig(
                key="user_need_verify",
                value={
                    "enabled": False,
                    "description": "用户注册是否需要管理员审核"
                },
                description="控制新用户注册后是否需要管理员审核才能激活账户"
            )
            db.add(user_verify_config)
            print("✓ 创建用户审核配置: user_need_verify")
        else:
            print("✓ 用户审核配置已存在: user_need_verify")
        
        # 供应商注册审核配置
        supplier_verify_config = db.query(SystemConfig).filter(
            SystemConfig.key == "supplier_need_verify"
        ).first()
        
        if not supplier_verify_config:
            supplier_verify_config = SystemConfig(
                key="supplier_need_verify",
                value={
                    "enabled": False,
                    "description": "供应商注册是否需要管理员审核"
                },
                description="控制新供应商注册后是否需要管理员审核才能参与报价"
            )
            db.add(supplier_verify_config)
            print("✓ 创建供应商审核配置: supplier_need_verify")
        else:
            print("✓ 供应商审核配置已存在: supplier_need_verify")
        
        db.commit()
        
        # 显示当前配置状态
        print("\n=== 当前审核配置状态 ===")
        user_enabled = user_verify_config.value.get("enabled", False)
        supplier_enabled = supplier_verify_config.value.get("enabled", False)
        
        print(f"用户注册审核: {'启用' if user_enabled else '禁用'}")
        print(f"供应商注册审核: {'启用' if supplier_enabled else '禁用'}")
        
        print("\n=== 配置说明 ===")
        print("- user_need_verify=true: 新用户注册后需要管理员审核才能激活")
        print("- supplier_need_verify=true: 新供应商注册后需要管理员审核才能参与报价")
        print("- 可通过系统配置管理页面修改这些设置")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 初始化配置失败: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("正在初始化审核配置...")
    init_audit_configs()
    print("✅ 审核配置初始化完成！")
