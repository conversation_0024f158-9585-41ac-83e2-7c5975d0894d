from typing import Generator, Callable, Optional
from fastapi import Depends, HTTPException, status, Request, Header
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session
import uuid

from app.db.session import SessionLocal
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.core.config import settings
from app.schemas.token import TokenPayload, TokenData

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

class OptionalOAuth2PasswordBearer(OAuth2PasswordBearer):
    async def __call__(self, request: Request) -> Optional[str]:
        try:
            return await super().__call__(request)
        except HTTPException:
            return None

optional_oauth2_scheme = OptionalOAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    user_id = uuid.UUID(token_data.sub)
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    return user

def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    return current_user

async def get_current_user_optional(
    db: Session = Depends(get_db), token: str = None, request: Request = None
) -> User:
    """
    获取当前用户，但不强制要求登录
    如果未登录，返回None
    允许从token参数或request头部获取令牌
    """
    # 如果未提供token，尝试从请求头获取
    if not token and request:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]

    if not token:
        return None

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        return None

    user_id = uuid.UUID(token_data.sub)
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.is_active:
        return None

    return user

async def get_current_active_user_or_guest(
    db: Session = Depends(get_db), 
    token: Optional[str] = Depends(optional_oauth2_scheme),
) -> Optional[User]:
    """
    获取当前用户，但允许匿名访问
    如果用户已登录，则返回用户对象
    如果用户未登录，则返回None
    """
    if not token:
        return None
    
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        return None
        
    user_id = uuid.UUID(token_data.sub)
    user = db.query(User).filter(User.id == user_id).first()
    
    # 只有当用户存在且激活时才返回用户对象
    if user and user.is_active:
        return user
    return None

def check_permission(user: User, resource: str, action: str, db: Session) -> bool:
    """
    检查用户是否有特定权限

    Args:
        user: 用户对象
        resource: 资源类型，如"task", "quote", "user"等
        action: 操作类型，如"read", "create", "update", "delete"等
        db: 数据库会话

    Returns:
        bool: 是否有权限
    """
    # 总部管理员和超级管理员拥有所有权限
    if user.level >= 4:
        return True

    # 检查用户的所有角色
    for role in user.roles:
        # 检查角色的所有权限
        for permission in role.permissions:
            if permission.resource == resource and permission.action == action:
                return True

    return False

def require_permission(resource: str, action: str) -> Callable:
    """
    创建一个依赖函数，用于检查用户是否有特定权限

    Args:
        resource: 资源类型，如"task", "quote", "user"等
        action: 操作类型，如"read", "create", "update", "delete"等

    Returns:
        依赖函数
    """
    def dependency(
        db: Session = Depends(get_db),
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        if not check_permission(current_user, resource, action, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有足够的权限执行此操作",
            )
        return current_user

    return dependency

def assign_default_roles(user: User, db: Session) -> None:
    """
    根据用户级别自动分配默认角色

    Args:
        user: 用户对象
        db: 数据库会话
    """
    # 获取或创建默认角色
    roles = []

    # 所有用户都有基本用户角色
    basic_role = db.query(Role).filter(Role.name == "basic_user").first()
    if basic_role:
        roles.append(basic_role)

    # 根据用户级别分配角色
    if user.level >= 2:  # 部门主管
        manager_role = db.query(Role).filter(Role.name == "department_manager").first()
        if manager_role:
            roles.append(manager_role)

    if user.level >= 3:  # 分公司负责人
        branch_role = db.query(Role).filter(Role.name == "branch_manager").first()
        if branch_role:
            roles.append(branch_role)

    if user.level >= 4:  # 总部管理员
        admin_role = db.query(Role).filter(Role.name == "admin").first()
        if admin_role:
            roles.append(admin_role)

    if user.level >= 5:  # 超级管理员
        super_admin_role = db.query(Role).filter(Role.name == "super_admin").first()
        if super_admin_role:
            roles.append(super_admin_role)

    # 分配角色
    user.roles = roles
    db.add(user)
    db.commit()