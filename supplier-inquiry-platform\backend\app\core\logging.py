"""
日志配置
"""
import os
import logging
import logging.handlers
from typing import Optional

# 日志文件路径
LOG_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../logs'))
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

APP_LOG_FILE = os.path.join(LOG_DIR, 'app.log')
ERROR_LOG_FILE = os.path.join(LOG_DIR, 'error.log')
API_LOG_FILE = os.path.join(LOG_DIR, 'api.log')

# 日志级别映射
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

# 格式化器
DETAILED_FORMATTER = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(module)s:%(lineno)d] - %(message)s'
)
SIMPLE_FORMATTER = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def setup_logging(log_level: str = 'info', log_to_console: bool = True, log_to_file: bool = True):
    """
    设置日志系统
    
    Args:
        log_level: 日志级别，可以是'debug', 'info', 'warning', 'error', 'critical'
        log_to_console: 是否输出到控制台
        log_to_file: 是否输出到文件
    """
    # 获取日志级别
    level = LOG_LEVELS.get(log_level.lower(), logging.INFO)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台输出
    if log_to_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(SIMPLE_FORMATTER)
        root_logger.addHandler(console_handler)
    
    # 文件输出
    if log_to_file:
        # 普通日志文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            APP_LOG_FILE, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(DETAILED_FORMATTER)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            ERROR_LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(DETAILED_FORMATTER)
        root_logger.addHandler(error_handler)
        
        # API日志文件处理器
        api_handler = logging.handlers.RotatingFileHandler(
            API_LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        api_handler.setLevel(logging.INFO)
        api_handler.setFormatter(DETAILED_FORMATTER)
        
        # 为API日志创建专门的日志器
        api_logger = logging.getLogger('app.api')
        api_logger.addHandler(api_handler)
    
    # 设置一些库的日志级别
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    
    logging.info(f"日志系统初始化完成，级别: {log_level}")

def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logger: 日志器实例
    """
    return logging.getLogger(name) 