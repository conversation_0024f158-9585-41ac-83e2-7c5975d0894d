from typing import Optional, List
from pydantic import BaseModel, UUID4
from datetime import datetime

class CompanyBase(BaseModel):
    """公司基础模型"""
    name: str
    address: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    is_supplier: Optional[bool] = False
    is_blacklisted: Optional[bool] = False
    is_verified: Optional[bool] = True

class CompanyCreate(CompanyBase):
    """创建公司模型"""
    pass

class CompanyUpdate(BaseModel):
    """更新公司模型"""
    name: Optional[str] = None
    address: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    is_supplier: Optional[bool] = None
    is_blacklisted: Optional[bool] = None
    is_verified: Optional[bool] = None

class CompanyInDBBase(CompanyBase):
    """数据库中的公司基础模型"""
    id: UUID4
    created_at: datetime
    updated_at: datetime
    is_blacklisted: Optional[bool] = False
    is_verified: Optional[bool] = True

    class Config:
        from_attributes = True

class Company(CompanyInDBBase):
    """返回给API的公司模型"""
    pass
