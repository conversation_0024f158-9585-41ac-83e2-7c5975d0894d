-- 供应商黑名单与注册审核功能数据库迁移脚本
-- 适用于PostgreSQL

-- 1. company表新增字段
ALTER TABLE company ADD COLUMN is_blacklisted BOOLEAN DEFAULT false;
ALTER TABLE company ADD COLUMN is_verified BOOLEAN DEFAULT true;

-- 2. 新增system_config表
CREATE TABLE system_config (
  id SERIAL PRIMARY KEY,
  supplier_need_verify BOOLEAN DEFAULT false
);

-- =============================
-- 适用于SQLite
-- =============================
-- SQLite不支持ALTER TABLE ... ADD COLUMN ... DEFAULT ... (带表达式)，需分步执行
-- 1. company表新增字段
ALTER TABLE company ADD COLUMN is_blacklisted BOOLEAN;
UPDATE company SET is_blacklisted = 0 WHERE is_blacklisted IS NULL;
ALTER TABLE company ADD COLUMN is_verified BOOLEAN;
UPDATE company SET is_verified = 1 WHERE is_verified IS NULL;

-- 2. 新增system_config表
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  supplier_need_verify BOOLEAN DEFAULT 0
); 