from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import uuid
import logging

from app.api import deps
from app.models.user import User
from app.models.role import Role
from app.models.task import Task
from app.models.quote import Quote
from app.schemas.user import User as UserSchema, UserCreate, UserUpdate
from app.schemas.role import Role as RoleSchema
from app.core.security import get_password_hash

router = APIRouter()

@router.get("/", response_model=List[UserSchema])
def read_users(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户列表
    """
    # 只有管理员可以查看所有用户
    if current_user.level < 3:  # 不是管理员
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 根据用户级别过滤
    if current_user.level == 3:  # 分公司负责人
        # 只能查看自己公司的用户
        users = db.query(User).filter(User.company_id == current_user.company_id).offset(skip).limit(limit).all()
    else:  # 总部管理员
        users = db.query(User).offset(skip).limit(limit).all()

    return users

@router.get("/team", response_model=List[UserSchema])
def read_team_members(
    db: Session = Depends(deps.get_db),
    current_user: Optional[User] = Depends(deps.get_current_active_user_or_guest),
) -> Any:
    """
    获取当前用户的团队成员（下属）
    """
    # 导入logging
    logger = logging.getLogger(__name__)
    
    try:
        # 如果用户未登录，返回空列表
        if not current_user:
            logger.info("未登录用户访问团队成员API，返回空列表")
            return []
            
        logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 请求团队成员数据")
        
        # 递归查找所有下属
        def find_subordinates(user_id, result=None):
            if result is None:
                result = []

            # 查找直接下属
            try:
                direct_subordinates = db.query(User).filter(User.parent_id == user_id).all()
                logger.debug(f"用户 {user_id} 的直接下属数量: {len(direct_subordinates)}")
                
                # 使用集合来防止重复
                for sub in direct_subordinates:
                    if sub not in result:
                        result.append(sub)

                # 递归查找间接下属
                for subordinate in direct_subordinates:
                    find_subordinates(subordinate.id, result)

                return result
            except Exception as e:
                logger.error(f"查询下属时出错: {str(e)}")
                return result

        # 获取所有下属
        subordinates = []
        try:
            subordinates = find_subordinates(current_user.id)
            logger.info(f"找到下属数量: {len(subordinates)}")
        except Exception as e:
            logger.error(f"查找下属失败: {str(e)}")
            subordinates = []
        
        # 添加当前用户自己
        result_list = []
        
        # 先添加当前用户
        try:
            current_user_obj = db.query(User).filter(User.id == current_user.id).first()
            if current_user_obj:
                result_list.append(current_user_obj)
                logger.debug(f"添加当前用户到结果: {current_user_obj.username}")
            else:
                logger.warning(f"无法获取当前用户信息: {current_user.id}")
        except Exception as e:
            logger.error(f"获取当前用户信息失败: {str(e)}")
        
        # 然后添加所有下属，确保不重复
        for sub in subordinates:
            if sub not in result_list:
                result_list.append(sub)
                
        logger.info(f"团队成员API返回总成员数: {len(result_list)}")
        return result_list
            
    except Exception as e:
        logger.error(f"获取团队成员时发生错误: {str(e)}")
        # 发生错误时返回空列表而不是抛出异常
        return []

@router.get("/{user_id}", response_model=UserSchema)
def read_user(
    user_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户详情
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 权限检查：只能查看自己或下属的信息
    if user.id != current_user.id and not is_subordinate(db, current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    return user

@router.post("/", response_model=UserSchema)
def create_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新用户
    """
    # 权限检查：只有管理员可以创建用户
    if current_user.level < 2:  # 不是管理员
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 检查用户名是否已存在
    user = db.query(User).filter(User.username == user_in.username).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    user = db.query(User).filter(User.email == user_in.email).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在",
        )

    # 权限检查：不能创建权限高于自己的用户
    if user_in.level > current_user.level:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能创建权限高于自己的用户",
        )

    # 创建新用户
    user = User(
        username=user_in.username,
        email=user_in.email,
        hashed_password=get_password_hash(user_in.password),
        name=user_in.name,
        phone=user_in.phone,
        company_id=user_in.company_id,
        parent_id=user_in.parent_id or current_user.id,  # 如果没有指定上级，则默认为当前用户
        level=user_in.level,
        is_active=user_in.is_active,
    )
    db.add(user)
    db.commit()
    db.refresh(user)

    # 分配默认角色
    deps.assign_default_roles(user, db)

    return user

@router.put("/{user_id}", response_model=UserSchema)
def update_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: uuid.UUID,
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新用户信息
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 权限检查：只能更新自己或下属的信息
    if user.id != current_user.id and not is_subordinate(db, current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 权限检查：不能将用户权限提升到高于自己的级别
    if user_in.level is not None and user_in.level > current_user.level:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能将用户权限提升到高于自己的级别",
        )

    # 更新用户信息
    update_data = user_in.dict(exclude_unset=True)

    # 如果更新密码，需要哈希处理
    if "password" in update_data:
        hashed_password = get_password_hash(update_data["password"])
        del update_data["password"]
        update_data["hashed_password"] = hashed_password

    for field, value in update_data.items():
        setattr(user, field, value)

    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@router.delete("/{user_id}", response_model=UserSchema)
def delete_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除用户（硬删除，直接从数据库中删除）
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 权限检查：只有管理员可以删除用户，且不能删除自己
    if current_user.level < 3 or user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 权限检查：不能删除权限高于自己的用户
    if user.level > current_user.level:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能删除权限高于自己的用户",
        )

    # 检查用户是否有下属，如果有，需要先处理下属关系
    subordinates = db.query(User).filter(User.parent_id == user_id).all()
    if subordinates:
        # 将下属的上级设为被删除用户的上级（或设为None）
        for subordinate in subordinates:
            subordinate.parent_id = user.parent_id
            db.add(subordinate)

    # 检查用户是否有创建的任务
    tasks_count = db.query(Task).filter(Task.creator_id == user_id).count()
    if tasks_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户存在 {tasks_count} 个相关任务，无法删除。请先处理相关任务。",
        )

    # 检查用户是否有创建的报价
    quotes_count = db.query(Quote).filter(Quote.created_by_id == user_id).count()
    if quotes_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户存在 {quotes_count} 个相关报价，无法删除。请先处理相关报价。",
        )

    # 保存用户信息用于返回
    user_data = UserSchema.from_orm(user)

    # 清除用户的角色关联
    user.roles.clear()
    db.commit()

    # 硬删除用户
    db.delete(user)
    db.commit()

    return user_data

# 辅助函数：检查用户是否是当前用户的下属
def is_subordinate(db: Session, current_user: User, user: User) -> bool:
    """
    检查用户是否是当前用户的下属
    """
    # 如果当前用户是总部管理员或超级管理员，可以管理所有用户
    if current_user.level >= 4:
        return True

    # 如果当前用户是分公司负责人，可以管理同公司的用户
    if current_user.level == 3 and current_user.company_id == user.company_id:
        return True

    # 如果当前用户是部门主管，可以管理直接下属
    if current_user.level == 2 and user.parent_id == current_user.id:
        return True

    # 递归检查是否是间接下属
    if user.parent_id:
        parent = db.query(User).filter(User.id == user.parent_id).first()
        if parent and parent.id == current_user.id:
            return True
        if parent:
            return is_subordinate(db, current_user, parent)

    return False

@router.post("/{user_id}/roles")
def assign_roles_to_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: uuid.UUID,
    role_ids: List[uuid.UUID],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    分配角色给用户
    """
    # 权限检查
    if not deps.check_permission(current_user, "user", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 权限检查：只能更新自己或下属的信息
    if user.id != current_user.id and not is_subordinate(db, current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    roles = db.query(Role).filter(Role.id.in_(role_ids)).all()
    user.roles = roles

    db.add(user)
    db.commit()
    db.refresh(user)
    return {"message": "角色分配成功"}

@router.get("/{user_id}/roles", response_model=List[RoleSchema])
def get_user_roles(
    *,
    db: Session = Depends(deps.get_db),
    user_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户的角色列表
    """
    # 权限检查
    if not deps.check_permission(current_user, "user", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 权限检查：只能查看自己或下属的信息
    if user.id != current_user.id and not is_subordinate(db, current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    return user.roles
