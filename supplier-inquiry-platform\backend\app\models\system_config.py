from sqlalchemy import Column, String, Text
from sqlalchemy.dialects.postgresql import JSON
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID

class SystemConfig(BaseModel):
    """系统配置模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key = Column(String, unique=True, nullable=False, index=True)
    value = Column(JSON, nullable=False)
    description = Column(Text) 