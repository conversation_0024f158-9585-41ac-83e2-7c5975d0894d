from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from sqlalchemy.orm import Session
import uuid
import json
from datetime import datetime

from app.api import deps
from app.models.user import User
from app.models.task import Task
from app.schemas.task import Task as TaskSchema, TaskCreate, TaskUpdate
from app.utils.field_validator import validate_task_fields
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

def is_task_accessible(db: Session, current_user: User, task: Task) -> bool:
    """
    检查当前用户是否有权限访问任务
    """
    # 任务创建者可以访问
    if task.creator_id == current_user.id:
        return True

    # 总部管理员可以访问所有任务
    if current_user.level >= 4:
        return True

    # 分公司负责人可以访问本公司的任务
    if current_user.level >= 3 and current_user.company_id == task.company_id:
        return True

    # 部门主管可以访问本部门的任务
    if current_user.level >= 2:
        # 获取任务创建者
        creator = db.query(User).filter(User.id == task.creator_id).first()
        if creator and creator.parent_id == current_user.id:
            return True

    return False

@router.get("/", response_model=List[TaskSchema])
def read_tasks(
    request: Request,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取任务列表
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求获取任务列表, 状态过滤: {status}, 分页: {skip}-{limit}")

    # 构建查询
    query = db.query(Task)

    # 根据用户级别过滤
    if current_user.level < 4:  # 不是总部管理员
        if current_user.level == 3:  # 分公司负责人
            # 可以查看本公司的任务
            query = query.filter(Task.company_id == current_user.company_id)
            logger.debug(f"分公司负责人[{current_user.id}]查看公司[{current_user.company_id}]的任务")
        elif current_user.level == 2:  # 部门主管
            # 可以查看下属的任务
            subordinates = db.query(User.id).filter(User.parent_id == current_user.id)
            query = query.filter(Task.creator_id.in_(subordinates))
            logger.debug(f"部门主管[{current_user.id}]查看下属创建的任务")
        else:  # 普通用户
            # 只能查看自己创建的任务
            query = query.filter(Task.creator_id == current_user.id)
            logger.debug(f"普通用户[{current_user.id}]查看自己创建的任务")

    # 根据状态过滤
    if status:
        query = query.filter(Task.status == status)

    # 分页
    tasks = query.offset(skip).limit(limit).all()
    logger.info(f"获取到 {len(tasks)} 条任务记录")

    return tasks

@router.get("/shared/{task_id}", response_model=TaskSchema)
def read_shared_task(
    request: Request,
    task_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取分享的任务详情（无需登录）
    """
    client_ip = request.client.host if request.client else "unknown"
    logger.info(f"匿名用户[{client_ip}]请求获取共享任务 ID: {task_id}")

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        logger.warning(f"共享任务不存在 ID: {task_id}, 请求来自IP: {client_ip}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 检查任务是否允许游客访问
    if not task.allow_guest:
        logger.warning(f"尝试访问非共享任务 ID: {task_id}, 请求来自IP: {client_ip}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此任务不允许游客访问",
        )

    logger.info(f"成功访问共享任务 ID: {task_id}, 标题: {task.title}, 请求来自IP: {client_ip}")
    return task

@router.get("/{task_id}", response_model=TaskSchema)
def read_task(
    request: Request,
    task_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取任务详情
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求获取任务 ID: {task_id}")

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        logger.warning(f"任务不存在 ID: {task_id}, 用户: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查
    if not is_task_accessible(db, current_user, task):
        logger.warning(f"用户[{current_user.id}|{current_user.username}]无权访问任务 ID: {task_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    logger.debug(f"用户[{current_user.id}]成功获取任务 ID: {task_id}, 标题: {task.title}")
    return task

@router.post("/", response_model=TaskSchema)
def create_task(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    task_in: TaskCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新任务
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]创建新任务: {task_in.title}")

        # 验证字段
    try:
        validate_task_fields(task_in.fields)
    except ValueError as e:
        logger.warning(f"创建任务字段验证失败: {str(e)}, 用户: {current_user.id}")
        raise

    # 创建任务
    task = Task(
        title=task_in.title,
        description=task_in.description,
        creator_id=current_user.id,
        company_id=task_in.company_id or current_user.company_id,
        fields=task_in.fields,
        deadline=task_in.deadline,
        allow_guest=task_in.allow_guest,
        status=task_in.status,
        publish_time=datetime.now() if task_in.status == "active" else None,
    )
    db.add(task)
    db.commit()
    db.refresh(task)

    logger.info(f"任务创建成功 ID: {task.id}, 标题: {task.title}, 截止日期: {task.deadline}, 创建者: {current_user.id}")
    # 记录任务的字段定义
    logger.debug(f"任务字段定义: {json.dumps(task.fields, ensure_ascii=False)}")

    return task

@router.put("/{task_id}", response_model=TaskSchema)
def update_task(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    task_id: uuid.UUID,
    task_in: TaskUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新任务
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]更新任务 ID: {task_id}")

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        logger.warning(f"要更新的任务不存在 ID: {task_id}, 用户: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查：只有任务创建者或管理员可以更新任务
    if task.creator_id != current_user.id and current_user.level < 3:
        logger.warning(f"用户[{current_user.id}]无权更新任务 ID: {task_id}, 创建者: {task.creator_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 验证字段
    if "fields" in task_in.dict(exclude_unset=True):
        try:
            validate_task_fields(task_in.fields)
        except ValueError as e:
            logger.warning(f"更新任务字段验证失败: {str(e)}, 任务ID: {task_id}, 用户: {current_user.id}")
            raise

    # 更新前记录任务状态
    logger.debug(f"更新前任务状态: ID: {task.id}, 标题: {task.title}, 状态: {task.status}")

    # 更新任务
    update_data = task_in.dict(exclude_unset=True)

    # 如果状态从非active变为active，设置发布时间
    if "status" in update_data and update_data["status"] == "active" and task.status != "active":
        update_data["publish_time"] = datetime.now()

    for field in update_data:
        setattr(task, field, update_data[field])

    db.add(task)
    db.commit()
    db.refresh(task)

    logger.info(f"任务更新成功 ID: {task.id}, 标题: {task.title}, 操作者: {current_user.id}")
    # 记录更新了哪些字段
    logger.debug(f"更新的字段: {list(update_data.keys())}")

    return task

@router.delete("/{task_id}", response_model=TaskSchema)
def delete_task(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    task_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除任务
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求删除任务 ID: {task_id}")

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        logger.warning(f"要删除的任务不存在 ID: {task_id}, 用户: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查：只有任务创建者或管理员可以删除任务
    if task.creator_id != current_user.id and current_user.level < 3:
        logger.warning(f"用户[{current_user.id}]无权删除任务 ID: {task_id}, 创建者: {task.creator_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 记录删除前的任务信息
    logger.debug(f"即将删除任务: ID: {task.id}, 标题: {task.title}, 创建者: {task.creator_id}, 创建时间: {task.created_at}")

    db.delete(task)
    db.commit()

    logger.info(f"任务删除成功 ID: {task_id}, 标题: {task.title}, 操作者: {current_user.id}")
    return task

@router.post("/{task_id}/share", response_model=dict)
def share_task(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    task_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    生成任务分享链接
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求分享任务 ID: {task_id}")

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        logger.warning(f"要分享的任务不存在 ID: {task_id}, 用户: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查
    if not is_task_accessible(db, current_user, task):
        logger.warning(f"用户[{current_user.id}]无权分享任务 ID: {task_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 生成分享链接
    # 这里简单返回任务ID，实际应用中可能需要生成带有签名的URL
    share_link = f"/share/task/{task_id}"

    logger.info(f"生成任务分享链接成功 ID: {task_id}, 标题: {task.title}, 操作者: {current_user.id}")
    return {"share_link": share_link}

@router.post("/import-excel", response_model=dict)
async def import_fields_from_excel(
    request: Request,
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    从Excel文件导入字段定义
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求从Excel导入字段, 文件名: {file.filename}")

    if not file.filename.endswith(('.xlsx', '.xls')):
        logger.warning(f"文件类型不支持: {file.filename}, 用户: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只支持Excel文件(.xlsx, .xls)",
        )

    try:
        # 这里需要安装pandas和openpyxl库
        import pandas as pd

        # 读取Excel文件
        df = pd.read_excel(file.file)
        logger.debug(f"成功读取Excel文件: {file.filename}, 包含 {len(df)} 行数据")

        # 检查必要的列
        required_columns = ["字段名称", "字段类型"]
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"Excel文件缺少必要的列: {col}, 用户: {current_user.id}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Excel文件缺少必要的列: {col}",
                )

        # 解析字段
        fields = []
        for _, row in df.iterrows():
            if pd.isna(row["字段名称"]) or pd.isna(row["字段类型"]):
                continue

            field = {
                "name": str(row["字段名称"]),
                "type": map_excel_type_to_system_type(str(row["字段类型"])),
                "required": False,
            }

            # 处理是否必填
            if "是否必填" in df.columns and not pd.isna(row["是否必填"]):
                field["required"] = str(row["是否必填"]) in ["是", "true", "True", "1"]

            # 处理选项
            if (field["type"] in ["select", "multiselect"] and
                "选项" in df.columns and not pd.isna(row["选项"])):
                options_text = str(row["选项"])
                options = [{"label": opt.strip(), "value": opt.strip()}
                          for opt in options_text.split(",") if opt.strip()]
                field["options"] = options

            fields.append(field)

        logger.info(f"导入字段成功: 共 {len(fields)} 个字段, 用户: {current_user.id}")
        logger.debug(f"导入的字段定义: {json.dumps(fields, ensure_ascii=False)}")

        return {"fields": fields}

    except Exception as e:
        logger.error(f"处理Excel文件时出错: {str(e)}, 文件名: {file.filename}, 用户: {current_user.id}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理Excel文件时出错: {str(e)}",
        )

def map_excel_type_to_system_type(excel_type: str) -> str:
    """
    将Excel中的类型映射到系统类型
    """
    type_map = {
        "文本": "string",
        "数字": "number",
        "布尔值": "boolean",
        "日期": "date",
        "单选": "select",
        "多选": "multiselect",
        "列表": "array"
    }

    return type_map.get(excel_type, "string")
