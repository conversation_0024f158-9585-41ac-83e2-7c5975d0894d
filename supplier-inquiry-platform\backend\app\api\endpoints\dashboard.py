from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import uuid
import logging

from app.api import deps
from app.models.user import User
from app.models.task import Task
from app.models.quote import Quote
from app.models.company import Company

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/stats")
def get_dashboard_stats(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取仪表板统计数据
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]请求仪表板统计数据")
    
    stats = {
        "my_tasks": 0,
        "pending_quotes": 0,
        "team_members": 0,
        "suppliers": 0
    }
    
    try:
        # 1. 我的任务数量
        logger.debug("开始查询任务数量")
        if current_user.level >= 4:  # 总部管理员及以上，查看所有任务
            my_tasks_count = db.query(Task).count()
            logger.debug(f"总部管理员查看所有任务: {my_tasks_count}")
        elif current_user.level == 3:  # 分公司负责人，查看本公司任务
            my_tasks_count = db.query(Task).filter(Task.company_id == current_user.company_id).count()
            logger.debug(f"分公司负责人查看公司任务: {my_tasks_count}")
        else:  # 普通用户，查看自己创建的任务
            my_tasks_count = db.query(Task).filter(Task.creator_id == current_user.id).count()
            logger.debug(f"普通用户查看自己任务: {my_tasks_count}")
        
        stats["my_tasks"] = my_tasks_count
        
        # 2. 待处理报价数量
        logger.debug("开始查询报价数量")
        if current_user.level >= 4:  # 总部管理员，查看所有报价
            pending_quotes_count = db.query(Quote).count()
            logger.debug(f"总部管理员查看所有报价: {pending_quotes_count}")
        elif current_user.level == 3:  # 分公司负责人，查看本公司任务的报价
            # 先获取本公司的任务
            company_tasks = db.query(Task.id).filter(Task.company_id == current_user.company_id).subquery()
            pending_quotes_count = db.query(Quote).filter(Quote.task_id.in_(company_tasks.select())).count()
            logger.debug(f"分公司负责人查看公司报价: {pending_quotes_count}")
        else:  # 普通用户，查看自己任务的报价
            my_tasks = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()
            pending_quotes_count = db.query(Quote).filter(Quote.task_id.in_(my_tasks.select())).count()
            logger.debug(f"普通用户查看自己任务报价: {pending_quotes_count}")
        
        stats["pending_quotes"] = pending_quotes_count
        
        # 3. 团队成员数量 (只有主管级别以上才显示)
        if current_user.level >= 2:
            logger.debug("开始查询团队成员数量")
            
            # 统一使用递归查找下属的逻辑，确保与团队管理页面数据一致
            def find_subordinates_count(user_id, visited=None):
                if visited is None:
                    visited = set()
                
                if user_id in visited:
                    return 0
                
                visited.add(user_id)
                count = 0
                
                # 查找直接下属
                direct_subordinates = db.query(User).filter(User.parent_id == user_id).all()
                count += len(direct_subordinates)
                
                # 递归查找间接下属
                for subordinate in direct_subordinates:
                    count += find_subordinates_count(subordinate.id, visited)
                
                return count
            
            subordinates_count = find_subordinates_count(current_user.id)
            team_members_count = subordinates_count + 1  # +1包括自己
            logger.debug(f"用户[{current_user.username}]团队成员数量（自己+所有下属）: {team_members_count}")
            
            stats["team_members"] = team_members_count
        
        # 4. 供应商数量 (只有分公司负责人以上才显示)
        if current_user.level >= 3:
            logger.debug("开始查询供应商数量")
            try:
                suppliers_count = db.query(Company).filter(Company.is_supplier == True).count()
                logger.debug(f"供应商数量: {suppliers_count}")
                stats["suppliers"] = suppliers_count
            except Exception as supplier_error:
                logger.error(f"查询供应商数量失败: {supplier_error}")
                # 如果供应商查询失败，设为0但不影响其他统计
                stats["suppliers"] = 0
        
        logger.info(f"仪表板统计数据: {stats}")
        return stats
        
    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计数据失败: {str(e)}"
        ) 