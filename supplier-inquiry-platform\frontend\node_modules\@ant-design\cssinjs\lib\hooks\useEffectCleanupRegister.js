"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _warning = require("rc-util/lib/warning");
var React = _interopRequireWildcard(require("react"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var fullClone = (0, _objectSpread2.default)({}, React);
var useInsertionEffect = fullClone.useInsertionEffect;

// DO NOT register functions in useEffect cleanup function, or functions that registered will never be called.
var useCleanupRegister = function useCleanupRegister(deps) {
  var effectCleanups = [];
  var cleanupFlag = false;
  function register(fn) {
    if (cleanupFlag) {
      if (process.env.NODE_ENV !== 'production') {
        (0, _warning.warning)(false, '[Ant Design CSS-in-JS] You are registering a cleanup function after unmount, which will not have any effect.');
      }
      return;
    }
    effectCleanups.push(fn);
  }
  React.useEffect(function () {
    // Compatible with strict mode
    cleanupFlag = false;
    return function () {
      cleanupFlag = true;
      if (effectCleanups.length) {
        effectCleanups.forEach(function (fn) {
          return fn();
        });
      }
    };
  }, deps);
  return register;
};
var useRun = function useRun() {
  return function (fn) {
    fn();
  };
};

// Only enable register in React 18
var useEffectCleanupRegister = typeof useInsertionEffect !== 'undefined' ? useCleanupRegister : useRun;
var _default = exports.default = useEffectCleanupRegister;