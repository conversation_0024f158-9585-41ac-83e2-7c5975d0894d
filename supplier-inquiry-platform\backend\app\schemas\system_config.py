from typing import Optional, Dict, Any
from pydantic import BaseModel, UUID4
from datetime import datetime

class SystemConfigBase(BaseModel):
    """系统配置基础模型"""
    key: str
    value: Dict[str, Any]
    description: Optional[str] = None

class SystemConfigCreate(SystemConfigBase):
    """创建系统配置模型"""
    pass

class SystemConfigUpdate(BaseModel):
    """更新系统配置模型"""
    value: Optional[Dict[str, Any]] = None
    description: Optional[str] = None

class SystemConfigInDBBase(SystemConfigBase):
    """数据库中的系统配置基础模型"""
    id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SystemConfig(SystemConfigInDBBase):
    """返回给API的系统配置模型"""
    pass 