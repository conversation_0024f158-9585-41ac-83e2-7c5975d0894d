import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout, Typography, Card, Alert, Spin, Result, Button, Steps, Form, Input, Divider, message } from 'antd';
import { CheckCircleOutlined, UserOutlined, FormOutlined, CheckOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import QuoteForm from '../components/QuoteForm';
import { getTaskById } from '../services/taskService';
import { createQuote } from '../services/quoteService';
import LogService from '../services/logService';
import api from '../services/api';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Step } = Steps;

/**
 * 报价提交页面
 * @returns {JSX.Element} - 渲染的页面
 */
const QuoteSubmitPage = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [supplierInfo, setSupplierInfo] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [supplierStatus, setSupplierStatus] = useState(null);
  const [checkingSupplier, setCheckingSupplier] = useState(false);

  // 组件挂载时记录日志
  useEffect(() => {
    LogService.info(`报价提交页面加载，任务ID: ${taskId || '未提供'}`, {
      url: window.location.pathname,
      params: { taskId }
    });

    return () => {
      LogService.debug(`报价提交页面卸载，任务ID: ${taskId || '未提供'}`);
    };
  }, [taskId]);

  // 获取任务数据
  useEffect(() => {
    const fetchTask = async () => {
      LogService.debug(`开始获取任务数据，任务ID: ${taskId}`);
      try {
        setLoading(true);
        const response = await getTaskById(taskId);

        // 优化任务数据响应处理
        if (!response || Object.keys(response).length === 0) {
          LogService.error(`任务数据响应为空，任务ID: ${taskId}`, { response });
          setError('获取任务数据失败，服务器返回空响应');
          setLoading(false);
          return;
        }

        LogService.info(`成功获取任务数据，任务ID: ${taskId}，任务标题: ${response.title}`, {
          taskTitle: response.title,
          taskDeadline: response.deadline,
          fieldCount: Object.keys(response.fields || {}).length,
        });

        setTask(response);

        // 检查任务状态和截止日期
        const now = new Date();
        const deadline = response.deadline ? new Date(response.deadline) : null;

        if (response.status !== 'active') {
          LogService.warn(`尝试访问非活动状态任务，任务ID: ${taskId}，状态: ${response.status}`);
          setError('此任务当前不接受报价');
        } else if (deadline && deadline < now) {
          LogService.warn(`尝试访问已过期任务，任务ID: ${taskId}，截止日期: ${response.deadline}`);
          setError('此任务已过截止日期，不再接受报价');
        }
      } catch (err) {
        // 详细记录错误
        LogService.error(`获取任务数据失败，任务ID: ${taskId}`, err);

        // 记录详细的网络错误信息
        if (err.request) {
          LogService.debug(`任务获取网络请求详情`, {
            url: err.config?.url,
            method: err.config?.method,
            status: err.response?.status,
            data: err.response?.data
          });
        }

        // 显示更友好的错误信息
        if (err.response?.status === 404) {
          setError('任务不存在或已被删除');
        } else if (err.response?.status === 403) {
          setError('此任务不允许游客访问，请登录后查看');
        } else {
          setError('获取任务信息失败，请检查链接是否正确或稍后再试');
        }
      } finally {
        setLoading(false);
      }
    };

    if (taskId) {
      fetchTask();
    } else {
      LogService.error('访问报价页面缺少必要的任务ID参数');
      setError('缺少任务ID，无法加载报价页面');
      setLoading(false);
    }
  }, [taskId]);

  // 检查供应商状态
  const checkSupplierStatus = async (companyName) => {
    try {
      setCheckingSupplier(true);
      LogService.info(`开始检查供应商状态，公司名称: ${companyName}`);

      // 调用专门的供应商状态检查API（游客可访问）
      const response = await api.get(`/companies/check-status?company_name=${encodeURIComponent(companyName)}`);

      LogService.info(`供应商状态检查响应:`, response);

      // 根据API响应设置状态
      const statusMap = {
        'new': {
          type: 'info',
          title: '新供应商',
          message: response.message,
          canProceed: response.can_proceed
        },
        'verified': {
          type: 'success',
          title: '供应商状态正常',
          message: response.message,
          canProceed: response.can_proceed
        },
        'blacklisted': {
          type: 'error',
          title: '供应商已被拉黑',
          message: response.message,
          canProceed: response.can_proceed
        },
        'pending': {
          type: 'warning',
          title: '供应商待审核',
          message: response.message,
          canProceed: response.can_proceed
        },
        'error': {
          type: 'warning',
          title: '状态检查失败',
          message: response.message,
          canProceed: response.can_proceed
        }
      };

      const statusInfo = statusMap[response.status] || statusMap['error'];
      setSupplierStatus(statusInfo);

      return response.can_proceed;

    } catch (error) {
      LogService.error(`检查供应商状态失败，公司名称: ${companyName}`, error);

      // 处理网络错误等异常情况
      setSupplierStatus({
        type: 'warning',
        title: '网络连接问题',
        message: '无法连接到服务器检查供应商状态，您可以继续提交报价。管理员会在审核时检查供应商资质。',
        canProceed: true
      });

      // 网络错误时允许继续，避免阻塞正常流程
      return true;
    } finally {
      setCheckingSupplier(false);
    }
  };

  // 处理供应商信息提交
  const handleSupplierInfoSubmit = async (values) => {
    LogService.info(`供应商信息填写完成，任务ID: ${taskId}`, {
      company_name: values.company_name,
      contact_person: values.contact_person,
      contact_email: values.contact_email
    });

    // 验证电子邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(values.contact_email)) {
      LogService.warn(`供应商提供了无效的电子邮箱格式: ${values.contact_email}`);
    }

    // 验证电话号码格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(values.contact_phone)) {
      LogService.warn(`供应商提供了可能无效的电话号码格式: ${values.contact_phone}`);
    }

    // 检查供应商状态
    const canProceed = await checkSupplierStatus(values.company_name);

    // 设置供应商信息
    setSupplierInfo(values);

    // 如果状态检查通过，自动进入下一步；否则停留在当前步骤
    if (canProceed) {
      setCurrentStep(1);
    }
  };

  // 处理报价提交
  const handleQuoteSubmit = async (values) => {
    LogService.info(`开始提交报价，任务ID: ${taskId}, 供应商: ${supplierInfo.company_name}`);
    try {
      setSubmitting(true);

      // 在提交前再次检查供应商状态（仅在有权限时进行）
      LogService.info(`提交前再次检查供应商状态，供应商: ${supplierInfo.company_name}`);
      try {
        const canProceed = await checkSupplierStatus(supplierInfo.company_name);
        if (!canProceed && supplierStatus && !supplierStatus.canProceed) {
          // 只有在明确检查到不能继续的状态时才阻止提交
          LogService.warn(`供应商状态检查失败，阻止报价提交，供应商: ${supplierInfo.company_name}`);
          message.error('供应商状态已发生变化，无法提交报价，请重新检查供应商信息');
          setSubmitting(false);
          setCurrentStep(0); // 返回第一步重新检查
          return;
        }
      } catch (error) {
        // 如果状态检查失败，记录日志但不阻止提交
        LogService.info(`提交前状态检查失败，但允许继续提交，供应商: ${supplierInfo.company_name}`, error);
      }

      // 检查任务是否存在自定义字段定义
      if (!task || !task.fields) {
        const errorMsg = '无法获取任务字段定义，请刷新页面重试';
        LogService.error(`任务字段定义不存在，无法提交报价，任务ID: ${taskId}`, {
          task: task ? JSON.stringify(task) : 'null',
          hasFields: task && !!task.fields
        });
        setError(errorMsg);
        setSubmitting(false);
        return;
      }

      // 验证必填字段
      const missingRequiredFields = [];
      Object.entries(task.fields).forEach(([key, field]) => {
        if (field.required && !values[key]) {
          missingRequiredFields.push(field.label || key);
        }
      });

      if (missingRequiredFields.length > 0) {
        const errorMsg = `缺少必填字段: ${missingRequiredFields.join(', ')}`;
        LogService.warn(`报价提交缺少必填字段，任务ID: ${taskId}`, {
          missingFields: missingRequiredFields
        });
        setError(errorMsg);
        setSubmitting(false);
        return;
      }

      // 合并供应商信息和报价数据
      const quoteData = {
        supplier_name: supplierInfo.company_name,
        contact_person: supplierInfo.contact_person,
        contact_phone: supplierInfo.contact_phone,
        contact_email: supplierInfo.contact_email,
        // 确保data是一个对象，并且只包含任务中定义的字段
        data: Object.keys(task.fields).reduce((acc, key) => {
          if (values[key] !== undefined) {
            acc[key] = values[key];
          }
          return acc;
        }, {}),
        is_registered: false,
      };

      LogService.debug(`准备提交报价数据，任务ID: ${taskId}`, {
        supplier_name: quoteData.supplier_name,
        fields_count: Object.keys(quoteData.data).length,
        field_names: Object.keys(quoteData.data)
      });

      // 记录请求开始时间，用于计算响应时间
      const requestStartTime = performance.now();

      // 发送API请求
      const response = await createQuote(taskId, quoteData);

      // 计算API响应时间
      const responseTime = performance.now() - requestStartTime;

      LogService.info(`报价提交成功，任务ID: ${taskId}，供应商: ${quoteData.supplier_name}`, {
        quote_id: response?.id || 'unknown',
        response_time_ms: responseTime.toFixed(2)
      });

      // 记录请求响应详情
      LogService.debug(`报价提交响应详情`, {
        status: 'success',
        response: response ? JSON.stringify(response) : 'empty',
        response_time_ms: responseTime.toFixed(2)
      });

      setSubmitted(true);
      setCurrentStep(2);
    } catch (err) {
      // 记录错误详情
      LogService.error(`报价提交失败，任务ID: ${taskId}`, err);

      // 详细记录网络错误
      if (err.request) {
        const errorResponseData = err.response?.data ? JSON.stringify(err.response.data) : 'no data';

        LogService.debug(`报价提交网络请求详情`, {
          url: err.config?.url,
          method: err.config?.method,
          status: err.response?.status,
          data: errorResponseData,
          request_payload: err.config?.data ? JSON.parse(err.config.data) : 'unknown'
        });
      }

      // 显示更友好的错误信息
      if (err.response?.status === 404) {
        setError('任务不存在或已被删除');
      } else if (err.response?.status === 403) {
        setError('此任务不允许游客提交报价，请登录后再试');
      } else if (err.response?.status === 400) {
        const errorDetail = err.response?.data?.detail || '报价数据格式不正确，请检查后重试';
        LogService.warn(`提交的报价数据格式有误`, {
          error: errorDetail,
          invalid_fields: err.response?.data?.invalid_fields || []
        });
        setError(errorDetail);
      } else if (err.response?.status === 500) {
        LogService.critical(`报价提交时服务器错误`, {
          task_id: taskId,
          error: err.response?.data || 'No error details'
        });
        setError('服务器处理报价时出错，请联系管理员');
      } else if (!err.response) {
        LogService.critical(`报价提交时网络连接失败`, {
          task_id: taskId,
          error: err.message || 'Network error'
        });
        setError('提交报价时网络连接失败，请检查您的网络连接');
      } else {
        setError('提交报价失败，请稍后重试');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card>
            <Title level={4}>填写供应商信息</Title>
            <Form
              layout="vertical"
              onFinish={handleSupplierInfoSubmit}
              initialValues={supplierInfo}
            >
              <Form.Item
                name="company_name"
                label="公司名称"
                rules={[{ required: true, message: '请输入公司名称' }]}
              >
                <Input placeholder="请输入公司名称" />
              </Form.Item>

              <Form.Item
                name="contact_person"
                label="联系人"
                rules={[{ required: true, message: '请输入联系人姓名' }]}
              >
                <Input placeholder="请输入联系人姓名" />
              </Form.Item>

              <Form.Item
                name="contact_phone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>

              <Form.Item
                name="contact_email"
                label="电子邮箱"
                rules={[
                  { required: true, message: '请输入电子邮箱' },
                  { type: 'email', message: '请输入有效的电子邮箱' }
                ]}
              >
                <Input placeholder="请输入电子邮箱" />
              </Form.Item>

              {/* 供应商状态检查loading */}
              {checkingSupplier && (
                <Alert
                  message="正在检查供应商状态..."
                  type="info"
                  showIcon
                  icon={<Spin size="small" />}
                  className="mb-4"
                />
              )}

              {/* 供应商状态提示 */}
              {supplierStatus && (
                <Alert
                  message={supplierStatus.title}
                  description={supplierStatus.message}
                  type={supplierStatus.type}
                  showIcon
                  className="mb-4"
                  icon={
                    supplierStatus.type === 'error' ? <CloseCircleOutlined /> :
                    supplierStatus.type === 'warning' ? <ExclamationCircleOutlined /> :
                    <CheckCircleOutlined />
                  }
                  action={
                    !supplierStatus.canProceed && (
                      <Button
                        size="small"
                        type="link"
                        onClick={() => {
                          setSupplierStatus(null);
                          setCurrentStep(0);
                        }}
                      >
                        修改信息
                      </Button>
                    )
                  }
                />
              )}

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  block
                  loading={checkingSupplier}
                  disabled={supplierStatus && !supplierStatus.canProceed}
                >
                  {supplierStatus && !supplierStatus.canProceed ? '无法继续' : '下一步'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        );

      case 1:
        return (
          <Card>
            <Title level={4}>填写报价信息</Title>
            <QuoteForm
              task={task}
              onSubmit={handleQuoteSubmit}
              loading={submitting}
            />
          </Card>
        );

      case 2:
        return (
          <Result
            status="success"
            title="报价提交成功！"
            subTitle={`感谢您参与"${task?.title}"的报价，我们已收到您的报价信息。`}
            extra={[
              <Button type="primary" key="home" onClick={() => navigate('/')}>
                返回首页
              </Button>,
            ]}
          />
        );

      default:
        return null;
    }
  };

  // 加载中
  if (loading) {
    return (
      <Content className="p-6 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Spin size="large" />
          <div className="mt-3">加载中...</div>
        </div>
      </Content>
    );
  }

  // 错误处理
  if (error) {
    return (
      <Content className="p-6">
        <Card>
          <Result
            status="error"
            title="无法提交报价"
            subTitle={error}
            extra={[
              <Button type="primary" key="home" onClick={() => navigate('/')}>
                返回首页
              </Button>,
            ]}
          />
        </Card>
      </Content>
    );
  }

  return (
    <Layout>
      <Content className="p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-6">
            <Title level={3}>{task?.title}</Title>
            <Text type="secondary">{task?.description}</Text>

            {task?.deadline && (
              <Alert
                message={`截止日期: ${new Date(task.deadline).toLocaleString()}`}
                type="info"
                showIcon
                className="mt-4"
              />
            )}
          </Card>

          <Card>
            <Steps current={currentStep} className="mb-8">
              <Step title="供应商信息" icon={<UserOutlined />} />
              <Step title="报价内容" icon={<FormOutlined />} />
              <Step title="提交完成" icon={<CheckOutlined />} />
            </Steps>

            {renderStepContent()}
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default QuoteSubmitPage;
