from sqlalchemy import Column, String, Table, ForeignKey
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID

# 角色-权限关联表
role_permission = Table(
    "role_permission",
    BaseModel.metadata,
    Column("role_id", UUID(as_uuid=True), ForeignKey("role.id"), primary_key=True),
    Column("permission_id", UUID(as_uuid=True), ForeignKey("permission.id"), primary_key=True),
)

class Role(BaseModel):
    """角色模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, unique=True, nullable=False)
    description = Column(String)
    
    # 关系
    users = relationship("User", secondary="user_role", back_populates="roles")
    permissions = relationship("Permission", secondary=role_permission, back_populates="roles")
