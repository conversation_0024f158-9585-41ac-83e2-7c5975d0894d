from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Header, Request
from sqlalchemy.orm import Session
import uuid
import json
from datetime import datetime
import statistics
from fastapi.responses import JSONResponse
import os
import shutil
from pathlib import Path

from app.api import deps
from app.models.user import User
from app.models.task import Task
from app.models.quote import Quote
from app.models.company import Company
from app.schemas.quote import Quote as QuoteSchema, QuoteCreate, QuoteUpdate, QuoteSummary, QuoteSummaryItem

router = APIRouter()

@router.get("/", response_model=List[QuoteSchema])
def read_quotes(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    task_id: Optional[uuid.UUID] = None,
    created_by_me: Optional[bool] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取报价列表
    """
    # 调试信息 - 函数调用参数
    print(f'DEBUG: read_quotes调用 - created_by_me={created_by_me}, user_level={current_user.level}, user_id={current_user.id}')
    print(f'DEBUG: 请求参数 - skip={skip}, limit={limit}, task_id={task_id}')
    
    # 构建查询
    query = db.query(Quote)

    # 根据任务ID筛选
    if task_id:
        query = query.filter(Quote.task_id == task_id)

    # 如果是"我的报价"，查询当前用户直接提交的报价
    if created_by_me:
        print('DEBUG: 执行我的报价查询逻辑')
        print(f'DEBUG: 我的报价筛选 - user_id={current_user.id}')
        query = query.filter(Quote.created_by_id == current_user.id)
    else:
        print('DEBUG: 执行全部报价查询逻辑')
        # "全部报价" - 根据用户级别过滤
        if current_user.level < 4:  # 不是总部管理员
            print(f'DEBUG: 非总部管理员权限过滤 - user_level={current_user.level}')
            # 获取用户可访问的任务ID列表
            accessible_tasks = []
            if current_user.level == 3:  # 分公司负责人
                print('DEBUG: 分公司负责人权限 - 查看本公司任务')
                # 可以查看本公司的任务
                tasks = db.query(Task).filter(Task.company_id == current_user.company_id).all()
                accessible_tasks = [task.id for task in tasks]
                print(f'DEBUG: 可访问任务数量 - {len(accessible_tasks)}')
            elif current_user.level == 2:  # 部门主管
                print('DEBUG: 部门主管权限 - 查看自己和下属任务')
                # 可以查看自己和下属的任务
                subordinates = db.query(User.id).filter(User.parent_id == current_user.id).all()
                subordinate_ids = [user.id for user in subordinates] + [current_user.id]  # 包含自己
                print(f'DEBUG: 管理的用户数量 - {len(subordinate_ids)}')
                tasks = db.query(Task).filter(Task.creator_id.in_(subordinate_ids)).all()
                accessible_tasks = [task.id for task in tasks]
                print(f'DEBUG: 可访问任务数量 - {len(accessible_tasks)}')
            else:  # 普通用户
                print('DEBUG: 普通用户权限 - 只能查看自己创建的任务')
                # 只能查看自己创建的任务
                tasks = db.query(Task).filter(Task.creator_id == current_user.id).all()
                accessible_tasks = [task.id for task in tasks]
                print(f'DEBUG: 可访问任务数量 - {len(accessible_tasks)}')

            # 筛选可访问的报价
            query = query.filter(Quote.task_id.in_(accessible_tasks))
        else:
            print('DEBUG: 总部管理员权限 - 可查看所有报价')

    # 查询结果统计
    quotes_count = query.count()
    print(f'DEBUG: 查询结果数量 - {quotes_count}')
    
    # 分页
    quotes = query.offset(skip).limit(limit).all()
    
    # 最终结果日志
    print(f'DEBUG: 返回报价数量 - {len(quotes)}')
    print(f'DEBUG: read_quotes执行完成')

    return quotes

@router.get("/{quote_id}", response_model=QuoteSchema)
def read_quote(
    quote_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取报价详情
    """
    quote = db.query(Quote).filter(Quote.id == quote_id).first()
    if not quote:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="报价不存在",
        )

    # 权限检查
    task = db.query(Task).filter(Task.id == quote.task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联任务不存在",
        )

    # 检查用户是否有权限查看此任务的报价
    if not is_task_accessible(db, current_user, task):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    return quote

@router.post("/tasks/{task_id}", response_model=QuoteSchema)
async def create_quote(
    task_id: uuid.UUID,
    quote_in: QuoteCreate,
    request: Request,
    db: Session = Depends(deps.get_db),
    token: str = Header(None),
) -> Any:
    """
    提交报价
    """
    try:
        current_user = None
        if token:
            current_user = await deps.get_current_user_optional(db=db, token=token, request=request)
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在",
            )
        if task.status != "active":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务当前状态不允许提交报价",
            )
        if task.deadline and datetime.now() > task.deadline:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务已过截止日期",
            )
        # 校验供应商黑名单和审核状态
        if quote_in.supplier_id:  # 只有当supplier_id不为None时才查询
            supplier = db.query(Company).filter(Company.id == quote_in.supplier_id).first()
            if supplier:
                if supplier.is_blacklisted:
                    raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="该供应商已被加入黑名单，禁止报价")
                if not supplier.is_verified:
                    raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="该供应商尚未审核通过，禁止报价")
        validate_quote_data(task.fields, quote_in.data)
        quote = Quote(
            task_id=task_id,
            supplier_name=quote_in.supplier_name,
            supplier_id=quote_in.supplier_id,
            data=quote_in.data,
            attachments=quote_in.attachments,
            is_registered=quote_in.is_registered,
            created_by_id=current_user.id if current_user else None,
            quote_time=datetime.now(),
        )
        db.add(quote)
        db.commit()
        db.refresh(quote)
        return quote
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误并返回500
        import traceback
        print(f"报价创建错误: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"内部服务器错误: {str(e)}"
        )

@router.put("/{quote_id}", response_model=QuoteSchema)
def update_quote(
    quote_id: uuid.UUID,
    quote_in: QuoteUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新报价
    """
    quote = db.query(Quote).filter(Quote.id == quote_id).first()
    if not quote:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="报价不存在",
        )

    # 权限检查：只有报价创建者或管理员可以更新报价
    if (quote.created_by_id and quote.created_by_id != current_user.id) and current_user.level < 3:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 检查任务是否允许更新报价
    task = db.query(Task).filter(Task.id == quote.task_id).first()
    if task.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务当前状态不允许更新报价",
        )

    # 检查任务是否已过截止日期
    if task.deadline and datetime.now() > task.deadline:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务已过截止日期",
        )

    # 更新报价
    update_data = quote_in.model_dump(exclude_unset=True)

    # 如果更新了数据，验证数据
    if "data" in update_data:
        validate_quote_data(task.fields, update_data["data"])

    for field, value in update_data.items():
        setattr(quote, field, value)

    db.add(quote)
    db.commit()
    db.refresh(quote)
    return quote

@router.delete("/{quote_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_quote(
    quote_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> None:
    """
    删除报价
    """
    quote = db.query(Quote).filter(Quote.id == quote_id).first()
    if not quote:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="报价不存在",
        )

    # 权限检查：只有报价创建者或管理员可以删除报价
    if (quote.created_by_id and quote.created_by_id != current_user.id) and current_user.level < 3:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    db.delete(quote)
    db.commit()

@router.get("/tasks/{task_id}", response_model=List[QuoteSchema])
def read_task_quotes(
    task_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取任务的所有报价
    """
    # 检查任务是否存在
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查
    if not is_task_accessible(db, current_user, task):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 获取任务的所有报价
    quotes = db.query(Quote).filter(Quote.task_id == task_id).offset(skip).limit(limit).all()

    return quotes

@router.get("/summary/{task_id}", response_model=QuoteSummary)
def get_quote_summary(
    task_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取报价汇总数据
    """
    # 检查任务是否存在
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在",
        )

    # 权限检查
    if not is_task_accessible(db, current_user, task):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 获取任务的所有报价
    quotes = db.query(Quote).filter(Quote.task_id == task_id).all()

    # 生成汇总数据
    summary_items = []
    for field_name, field_info in task.fields.items():
        # 检查field_info是否为字典类型
        if isinstance(field_info, dict):
            field_type = field_info.get("type", "text")
            field_label = field_info.get("label", field_name)
        else:
            # 如果不是字典，则使用默认值
            field_type = "text"
            field_label = field_name

        # 收集所有报价中此字段的值
        values = []
        for quote in quotes:
            if field_name in quote.data:
                value = quote.data[field_name]
                values.append({
                    "supplier_name": quote.supplier_name,
                    "value": value
                })

        # 计算数值型字段的统计信息
        min_value = None
        max_value = None
        avg_value = None
        if field_type == "number" and values:
            try:
                numeric_values = [float(item["value"]) for item in values if item["value"] is not None]
                if numeric_values:
                    min_value = min(numeric_values)
                    max_value = max(numeric_values)
                    avg_value = statistics.mean(numeric_values)
            except (ValueError, TypeError):
                # 忽略无法转换为数字的值
                pass

        summary_item = QuoteSummaryItem(
            field_name=field_name,
            field_label=field_label,
            field_type=field_type,
            values=values,
            min_value=min_value,
            max_value=max_value,
            avg_value=avg_value
        )
        summary_items.append(summary_item)

    summary = QuoteSummary(
        task_id=task.id,
        task_title=task.title,
        supplier_count=len(quotes),
        items=summary_items
    )

    return summary

@router.post("/upload", response_model=Dict[str, str])
async def upload_file(
    file: UploadFile = File(...),
    token: str = Header(None),
) -> Any:
    """
    上传报价附件
    """
    try:
        # 确保上传目录存在
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename
        
        # 保存上传的文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 返回文件URL
        file_url = f"/uploads/{unique_filename}"
        
        return {
            "url": file_url,
            "name": file.filename
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )

@router.post("/{quote_id}/attachments", response_model=Dict[str, Any])
async def upload_quote_attachment(
    quote_id: uuid.UUID,
    file: UploadFile = File(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user_or_guest),
) -> Any:
    """
    为指定报价上传附件
    """
    # 检查报价是否存在
    quote = db.query(Quote).filter(Quote.id == quote_id).first()
    if not quote:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="报价不存在",
        )
    
    # 权限检查：只有报价创建者或管理员可以上传附件
    if current_user and current_user.id != quote.created_by_id and current_user.level < 3:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )
        
    try:
        # 确保上传目录存在
        upload_dir = Path("uploads") / "quotes" / str(quote_id)
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename
        
        # 保存上传的文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 更新报价附件信息
        file_url = f"/uploads/quotes/{quote_id}/{unique_filename}"
        
        # 初始化附件字典（如果为空）
        if quote.attachments is None:
            quote.attachments = {}
        
        attachment_key = f"file_{len(quote.attachments) + 1}"
        quote.attachments[attachment_key] = {
            "name": file.filename,
            "url": file_url
        }
        
        db.add(quote)
        db.commit()
        db.refresh(quote)
        
        return {
            "success": True,
            "file": {
                "name": file.filename,
                "url": file_url
            },
            "attachments": quote.attachments
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"附件上传失败: {str(e)}"
        )

# 辅助函数

def is_task_accessible(db: Session, user: User, task: Task) -> bool:
    """
    检查用户是否有权限访问任务
    """
    if user.level >= 4:  # 总部管理员或超级管理员
        return True
    elif user.level == 3:  # 分公司负责人
        return task.company_id == user.company_id
    elif user.level == 2:  # 部门主管
        # 检查任务创建者是否是该用户的下属
        creator = db.query(User).filter(User.id == task.creator_id).first()
        return creator and creator.parent_id == user.id
    else:  # 普通用户
        return task.creator_id == user.id

def validate_quote_data(task_fields: Dict[str, Any], quote_data: Dict[str, Any]) -> None:
    """
    验证报价数据是否符合任务字段要求
    """
    # 检查必填字段
    for field_name, field_info in task_fields.items():
        # 修复: 检查field_info是否为字典类型，如果是字符串或其他类型则跳过
        if isinstance(field_info, dict):
            if field_info.get("required", False) and field_name not in quote_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少必填字段: {field_name}"
                )
        elif field_name not in quote_data:
            # 默认情况下，所有字段都视为非必填
            pass

    # 检查数据类型
    for field_name, value in quote_data.items():
        if field_name in task_fields:
            field_info = task_fields[field_name]
            # 修复: 只有当field_info是字典时才尝试获取type
            if isinstance(field_info, dict):
                field_type = field_info.get("type", "text")
            else:
                # 如果field_info不是字典，默认为文本类型
                field_type = "text"

            # 数字类型验证
            if field_type == "number" and value is not None:
                try:
                    float(value)
                except (ValueError, TypeError):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"字段 {field_name} 必须是数字类型"
                    )

            # 日期类型验证
            elif field_type == "date" and value is not None:
                try:
                    datetime.fromisoformat(value.replace("Z", "+00:00"))
                except (ValueError, TypeError):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"字段 {field_name} 必须是有效的日期格式"
                    )
