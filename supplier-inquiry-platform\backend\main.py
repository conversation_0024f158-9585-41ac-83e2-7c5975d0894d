import os
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.api import api_router
from app.core.config import settings
from app.db.init_db import init_db
from app.core.logging import setup_logging, get_logger
from app.middleware.logging_middleware import RequestLoggingMiddleware

# 初始化日志系统
setup_logging(
    log_level="debug",
    log_to_console=True,
    log_to_file=True
)

# 获取应用日志器
logger = get_logger(__name__)

# 初始化数据库
logger.info("初始化数据库")
init_db()

app = FastAPI(
    title="供应商询价平台API",
    description="供应商询价平台的后端API服务",
    version="0.1.0",
)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix="/api")

@app.get("/")
async def root():
    logger.info("首页API请求")
    return {"message": "供应商询价平台API服务运行中"}

if __name__ == "__main__":
    logger.info("启动应用服务器")
    uvicorn.run("main:app", host="0.0.0.0", port=5000, reload=True)
