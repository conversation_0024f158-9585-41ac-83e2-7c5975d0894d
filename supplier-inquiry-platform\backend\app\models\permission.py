from sqlalchemy import Column, String
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID

class Permission(BaseModel):
    """权限模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, unique=True, nullable=False)
    description = Column(String)
    resource = Column(String, nullable=False)  # 资源类型，如"task", "quote", "user"等
    action = Column(String, nullable=False)    # 操作类型，如"read", "create", "update", "delete"等
    
    # 关系
    roles = relationship("Role", secondary="role_permission", back_populates="permissions")
