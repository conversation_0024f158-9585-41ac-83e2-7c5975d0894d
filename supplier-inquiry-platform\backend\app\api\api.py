from fastapi import APIRouter
from app.api.endpoints import auth, users, tasks, quotes, roles, permissions, logs, companies, dashboard, system_config

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(quotes.router, prefix="/quotes", tags=["quotes"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
api_router.include_router(logs.router, prefix="/logs", tags=["logs"])
api_router.include_router(companies.router, prefix="/companies", tags=["companies"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(system_config.router, prefix="/system-config", tags=["system-config"])
