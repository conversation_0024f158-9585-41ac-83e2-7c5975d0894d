import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Tabs, Alert } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { login, register } from '../store/thunks/authThunks';
import api from '../services/api';

const { Title } = Typography;

const LoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { loading, error, isAuthenticated } = useSelector((state) => state.auth);

  // 注册配置状态
  const [registrationConfig, setRegistrationConfig] = useState({
    supplier_need_verify: false,
    message: "注册后可立即使用",
    loading: true,
    error: null
  });

  // 获取注册配置
  const fetchRegistrationConfig = async () => {
    try {
      setRegistrationConfig(prev => ({ ...prev, loading: true, error: null }));
      const response = await api.get('/system-config/public/registration-config');
      setRegistrationConfig({
        supplier_need_verify: response.supplier_need_verify,
        message: response.message,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('获取注册配置失败:', error);
      // 降级处理：使用默认配置
      setRegistrationConfig({
        supplier_need_verify: false,
        message: "注册后可立即使用",
        loading: false,
        error: "配置获取失败，使用默认设置"
      });
    }
  };

  // 如果已登录，重定向到首页或来源页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // 组件加载时获取注册配置
  useEffect(() => {
    fetchRegistrationConfig();
  }, []);

  // 处理登录表单提交
  const handleLogin = (values) => {
    dispatch(login({
      username: values.username,
      password: values.password
    }));
  };

  // 处理注册表单提交
  const handleRegister = (values) => {
    dispatch(register({
      username: values.username,
      email: values.email,
      password: values.password,
      name: values.name,
      level: 1, // 默认为普通用户
      is_active: true
    })).unwrap()
      .then(() => {
        // 根据配置显示不同的成功提示
        const successMessage = registrationConfig.supplier_need_verify
          ? "注册成功！请等待管理员审核后使用。"
          : "注册成功！正在为您登录...";

        console.log(successMessage);

        // 如果不需要审核，自动登录
        if (!registrationConfig.supplier_need_verify) {
          dispatch(login({
            username: values.username,
            password: values.password
          }));
        }
      });
  };

  // 登录表单组件
  const LoginForm = () => (
    <Form
      name="login"
      initialValues={{ remember: true }}
      onFinish={handleLogin}
      size="large"
    >
      <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="用户名" />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码!' }]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="密码" />
      </Form.Item>

      <Form.Item>
        <Form.Item name="remember" valuePropName="checked" noStyle>
          <Checkbox>记住我</Checkbox>
        </Form.Item>

        <a className="float-right" href="#">忘记密码</a>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="w-full"
          loading={loading}
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  // 注册表单组件
  const RegisterForm = () => (
    <Form
      name="register"
      onFinish={handleRegister}
      size="large"
    >
      {/* 注册审核提示 */}
      {!registrationConfig.loading && (
        <Alert
          message="注册提示"
          description={registrationConfig.message}
          type={registrationConfig.supplier_need_verify ? "info" : "success"}
          icon={<InfoCircleOutlined />}
          showIcon
          className="mb-4"
        />
      )}

      {/* 配置获取错误提示 */}
      {registrationConfig.error && (
        <Alert
          message="提示"
          description={registrationConfig.error}
          type="warning"
          showIcon
          className="mb-4"
        />
      )}
      <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="用户名" />
      </Form.Item>

      <Form.Item
        name="name"
        rules={[{ required: true, message: '请输入姓名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="姓名" />
      </Form.Item>

      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱!' },
          { type: 'email', message: '请输入有效的邮箱地址!' }
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="邮箱" />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码!' },
          { min: 6, message: '密码长度不能少于6个字符!' }
        ]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="密码" />
      </Form.Item>

      <Form.Item
        name="confirm"
        dependencies={['password']}
        rules={[
          { required: true, message: '请确认密码!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致!'));
            },
          }),
        ]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="w-full"
          loading={loading || registrationConfig.loading}
        >
          {registrationConfig.loading ? "加载中..." : "注册"}
        </Button>
      </Form.Item>
    </Form>
  );

  const tabItems = [
    {
      key: 'login',
      label: '登录',
      children: <LoginForm />
    },
    {
      key: 'register',
      label: '注册',
      children: <RegisterForm />
    }
  ];

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <div className="text-center mb-6">
          <Title level={3}>供应商询价平台</Title>
        </div>

        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            className="mb-4"
            closable
          />
        )}

        <Tabs defaultActiveKey="login" items={tabItems} />

        <div className="text-center mt-4">
          <Link to="/">返回首页</Link>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
