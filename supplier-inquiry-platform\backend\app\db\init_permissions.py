from sqlalchemy.orm import Session

from app.models.role import Role
from app.models.permission import Permission
from app.models.user import User

def init_roles_and_permissions(db: Session) -> None:
    """
    初始化角色和权限

    Args:
        db: 数据库会话
    """
    # 创建默认角色
    roles = {
        "basic_user": "基本用户",
        "department_manager": "部门主管",
        "branch_manager": "分公司负责人",
        "admin": "总部管理员",
        "super_admin": "超级管理员",
    }

    created_roles = {}
    for role_name, role_desc in roles.items():
        role = db.query(Role).filter(Role.name == role_name).first()
        if not role:
            role = Role(name=role_name, description=role_desc)
            db.add(role)
            db.flush()
        created_roles[role_name] = role

    # 创建默认权限
    permissions = [
        # 任务权限
        {"name": "task_read", "description": "查看任务", "resource": "task", "action": "read"},
        {"name": "task_create", "description": "创建任务", "resource": "task", "action": "create"},
        {"name": "task_update", "description": "更新任务", "resource": "task", "action": "update"},
        {"name": "task_delete", "description": "删除任务", "resource": "task", "action": "delete"},

        # 报价权限
        {"name": "quote_read", "description": "查看报价", "resource": "quote", "action": "read"},
        {"name": "quote_create", "description": "创建报价", "resource": "quote", "action": "create"},
        {"name": "quote_update", "description": "更新报价", "resource": "quote", "action": "update"},
        {"name": "quote_delete", "description": "删除报价", "resource": "quote", "action": "delete"},

        # 用户权限
        {"name": "user_read", "description": "查看用户", "resource": "user", "action": "read"},
        {"name": "user_create", "description": "创建用户", "resource": "user", "action": "create"},
        {"name": "user_update", "description": "更新用户", "resource": "user", "action": "update"},
        {"name": "user_delete", "description": "删除用户", "resource": "user", "action": "delete"},

        # 公司权限
        {"name": "company_read", "description": "查看公司", "resource": "company", "action": "read"},
        {"name": "company_create", "description": "创建公司", "resource": "company", "action": "create"},
        {"name": "company_update", "description": "更新公司", "resource": "company", "action": "update"},
        {"name": "company_delete", "description": "删除公司", "resource": "company", "action": "delete"},

        # 角色权限
        {"name": "role_read", "description": "查看角色", "resource": "role", "action": "read"},
        {"name": "role_create", "description": "创建角色", "resource": "role", "action": "create"},
        {"name": "role_update", "description": "更新角色", "resource": "role", "action": "update"},
        {"name": "role_delete", "description": "删除角色", "resource": "role", "action": "delete"},

        # 权限权限
        {"name": "permission_read", "description": "查看权限", "resource": "permission", "action": "read"},
        {"name": "permission_create", "description": "创建权限", "resource": "permission", "action": "create"},
        {"name": "permission_update", "description": "更新权限", "resource": "permission", "action": "update"},
        {"name": "permission_delete", "description": "删除权限", "resource": "permission", "action": "delete"},
    ]

    created_permissions = {}
    for perm_data in permissions:
        perm = db.query(Permission).filter(Permission.name == perm_data["name"]).first()
        if not perm:
            perm = Permission(**perm_data)
            db.add(perm)
            db.flush()
        created_permissions[perm_data["name"]] = perm

    # 分配权限给角色
    role_permissions = {
        "basic_user": [
            "task_read", "task_create", "task_update",
            "quote_read", "quote_create", "quote_update",
            "user_read",
            "company_read",
        ],
        "department_manager": [
            "task_read", "task_create", "task_update", "task_delete",
            "quote_read", "quote_create", "quote_update", "quote_delete",
            "user_read", "user_create", "user_update",
            "company_read",
        ],
        "branch_manager": [
            "task_read", "task_create", "task_update", "task_delete",
            "quote_read", "quote_create", "quote_update", "quote_delete",
            "user_read", "user_create", "user_update", "user_delete",
            "company_read", "company_create", "company_update",
            "role_read",
        ],
        "admin": [
            "task_read", "task_create", "task_update", "task_delete",
            "quote_read", "quote_create", "quote_update", "quote_delete",
            "user_read", "user_create", "user_update", "user_delete",
            "company_read", "company_create", "company_update", "company_delete",
            "role_read", "role_create", "role_update", "role_delete",
            "permission_read", "permission_create", "permission_update", "permission_delete",
        ],
        "super_admin": [
            "task_read", "task_create", "task_update", "task_delete",
            "quote_read", "quote_create", "quote_update", "quote_delete",
            "user_read", "user_create", "user_update", "user_delete",
            "company_read", "company_create", "company_update", "company_delete",
            "role_read", "role_create", "role_update", "role_delete",
            "permission_read", "permission_create", "permission_update", "permission_delete",
        ],
    }

    for role_name, perm_names in role_permissions.items():
        role = created_roles.get(role_name)
        if role:
            perms = [created_permissions.get(name) for name in perm_names if created_permissions.get(name)]
            role.permissions = perms
            db.add(role)

    db.commit()

    # 为现有用户分配角色
    users = db.query(User).all()
    for user in users:
        # 获取角色
        roles = []

        # 所有用户都有基本用户角色
        basic_role = created_roles.get("basic_user")
        if basic_role:
            roles.append(basic_role)

        # 根据用户级别分配角色
        if user.level >= 2:  # 部门主管
            manager_role = created_roles.get("department_manager")
            if manager_role:
                roles.append(manager_role)

        if user.level >= 3:  # 分公司负责人
            branch_role = created_roles.get("branch_manager")
            if branch_role:
                roles.append(branch_role)

        if user.level >= 4:  # 总部管理员
            admin_role = created_roles.get("admin")
            if admin_role:
                roles.append(admin_role)

        if user.level >= 5:  # 超级管理员
            super_admin_role = created_roles.get("super_admin")
            if super_admin_role:
                roles.append(super_admin_role)

        # 分配角色
        user.roles = roles
        db.add(user)

    db.commit()
    print("角色和权限初始化完成")
