"""
HTTP请求日志中间件
"""
import time
import logging
import json
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
from starlette.types import ASGIApp

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    HTTP请求日志中间件，记录所有请求和响应信息
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.logger = logging.getLogger("app.api.request")
        
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        # 获取请求信息
        request_id = str(time.time())
        client_host = request.client.host if request.client else "unknown"
        request_path = request.url.path
        request_query = request.url.query
        request_method = request.method
        
        # 记录请求开始
        self.logger.info(
            f"请求开始 - ID: {request_id} - {request_method} {request_path} - 来自: {client_host}"
        )
        
        # 记录详细查询参数
        if request_query:
            self.logger.debug(f"请求参数 - ID: {request_id} - Query: {request_query}")
        
        # 记录请求头
        headers_text = ", ".join([f"{k}: {v}" for k, v in request.headers.items() 
                                  if k.lower() not in ("authorization", "cookie")])
        self.logger.debug(f"请求头 - ID: {request_id} - Headers: {headers_text}")
        
        # 尝试记录请求体
        try:
            if request_method in ("POST", "PUT", "PATCH"):
                # 复制请求体，因为它只能被读取一次
                body = await request.body()
                # 把请求体放回去，这样后续处理器仍然可以读取
                async def receive():
                    return {"type": "http.request", "body": body}
                request._receive = receive
                
                try:
                    body_text = body.decode("utf-8")
                    if body_text and len(body_text) < 2000:  # 限制日志大小
                        # 尝试将其解析为JSON以便更好的记录
                        try:
                            body_json = json.loads(body_text)
                            # 隐藏敏感字段
                            if "password" in body_json:
                                body_json["password"] = "******"
                            body_text = json.dumps(body_json)
                        except:
                            pass
                        self.logger.debug(f"请求体 - ID: {request_id} - Body: {body_text}")
                except Exception as e:
                    self.logger.warning(f"无法记录请求体 - ID: {request_id} - 错误: {str(e)}")
        except Exception as e:
            self.logger.warning(f"处理请求体时出错 - ID: {request_id} - 错误: {str(e)}")
        
        # 记录处理时间
        start_time = time.time()
        
        try:
            # 调用下一个中间件或路由处理程序
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            status_code = response.status_code
            self.logger.info(
                f"请求完成 - ID: {request_id} - {request_method} {request_path} - "
                f"状态码: {status_code} - 耗时: {process_time:.4f}秒"
            )
            
            # 对于错误响应，尝试记录响应体
            if status_code >= 400:
                try:
                    response_body = b""
                    async for chunk in response.body_iterator:
                        response_body += chunk
                    
                    # 创建一个新的响应，因为原始响应的主体已被消费
                    response = Response(
                        content=response_body,
                        status_code=response.status_code,
                        headers=dict(response.headers),
                        media_type=response.media_type
                    )
                    
                    # 记录响应体
                    try:
                        body_text = response_body.decode("utf-8")
                        if body_text and len(body_text) < 2000:
                            self.logger.debug(f"错误响应体 - ID: {request_id} - Body: {body_text}")
                    except:
                        pass
                except Exception as e:
                    self.logger.warning(f"记录响应体时出错 - ID: {request_id} - 错误: {str(e)}")
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录异常
            self.logger.error(
                f"请求处理异常 - ID: {request_id} - {request_method} {request_path} - "
                f"错误: {str(e)} - 耗时: {process_time:.4f}秒"
            )
            
            # 重新抛出异常，让FastAPI的异常处理器处理
            raise 